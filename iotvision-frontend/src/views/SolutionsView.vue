<template>
  <div class="solutions-view">
    <section class="solutions-hero section">
      <div class="container">
        <div class="hero-content">
          <h1 class="hero-title">
            {{ $t('solutions.title') }}
          </h1>
          <p class="hero-subtitle">
            {{ $t('solutions.subtitle') }}
          </p>
        </div>
      </div>
    </section>

    <section class="solutions-grid section">
      <div class="container">
        <div class="solutions-list">
          <div 
            v-for="solution in solutions" 
            :key="solution.id"
            class="solution-card"
          >
            <div class="solution-icon">{{ solution.icon }}</div>
            <h3 class="solution-title">{{ $t(solution.titleKey) }}</h3>
            <p class="solution-description">{{ $t(solution.descriptionKey) }}</p>

            <div class="solution-features">
              <h4>Key Features:</h4>
              <ul>
                <li v-for="feature in solution.features" :key="feature">{{ feature }}</li>
              </ul>
            </div>
            
            <div class="solution-stats">
              <div class="stat">
                <span class="stat-value">{{ solution.stat.value }}</span>
                <span class="stat-label">{{ solution.stat.label }}</span>
              </div>
            </div>
            
            <RouterLink to="/contact" class="btn btn-outline solution-cta">
              {{ $t('common.learnMore') }}
            </RouterLink>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import { RouterLink } from 'vue-router'

const solutions = [
  {
    id: 1,
    titleKey: 'solutions.manufacturing.title',
    icon: '🏭',
    descriptionKey: 'solutions.manufacturing.description',
    features: [
      'Predictive maintenance',
      'Quality control automation',
      'Energy optimization',
      'Supply chain visibility'
    ],
    stat: {
      value: '40%',
      label: 'Efficiency Increase'
    }
  },
  {
    id: 2,
    titleKey: 'solutions.smartCities.title',
    icon: '🏙️',
    descriptionKey: 'solutions.smartCities.description',
    features: [
      'Traffic flow optimization',
      'Smart lighting systems',
      'Waste management',
      'Environmental monitoring'
    ],
    stat: {
      value: '30%',
      label: 'Energy Savings'
    }
  },
  {
    id: 3,
    titleKey: 'solutions.healthcare.title',
    icon: '🏥',
    descriptionKey: 'solutions.healthcare.description',
    features: [
      'Patient monitoring',
      'Asset tracking',
      'Environmental control',
      'Emergency response'
    ],
    stat: {
      value: '50%',
      label: 'Response Time Improvement'
    }
  },
  {
    id: 4,
    titleKey: 'solutions.agriculture.title',
    icon: '🌾',
    descriptionKey: 'solutions.agriculture.description',
    features: [
      'Soil monitoring',
      'Automated irrigation',
      'Crop health tracking',
      'Weather integration'
    ],
    stat: {
      value: '25%',
      label: 'Yield Increase'
    }
  }
]
</script>

<style scoped lang="scss">

.solutions-view {
  padding-top: 70px;
}

.solutions-hero {
  background: linear-gradient(135deg, rgba(26, 26, 46, 0.8) 0%, rgba(10, 10, 15, 0.9) 100%);
  text-align: center;
  padding: 4rem 0;
}

.hero-content {
  max-width: 800px;
  margin: 0 auto;
}

.hero-title {
  font-size: 3rem;
  margin-bottom: 1.5rem;
  
  @media (max-width: $breakpoint-md) {
    font-size: 2.5rem;
  }
}

.hero-subtitle {
  font-size: 1.3rem;
  color: $color-text-muted;
  line-height: 1.6;
}

.solutions-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.solution-card {
  background: rgba(26, 26, 46, 0.6);
  border: 1px solid rgba(0, 255, 255, 0.2);
  border-radius: $border-radius-lg;
  padding: 2rem;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  height: 100%;

  &:hover {
    border-color: rgba(0, 255, 255, 0.4);
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 255, 255, 0.2);
  }
}

.solution-icon {
  font-size: 3rem;
  margin-bottom: 1.5rem;
}

.solution-title {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: $color-text-white;
}

.solution-description {
  color: $color-text-muted;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.solution-features {
  margin-bottom: 1.5rem;
  
  h4 {
    color: $color-text-white;
    margin-bottom: 0.5rem;
    font-size: 1rem;
  }
  
  ul {
    list-style: none;
    
    li {
      position: relative;
      padding-left: 1.5rem;
      margin-bottom: 0.3rem;
      color: $color-text-muted;
      font-size: 0.9rem;
      
      &::before {
        content: '✓';
        position: absolute;
        left: 0;
        color: $color-neon-green;
        font-weight: bold;
      }
    }
  }
}

.solution-stats {
  margin-bottom: 1.5rem;
  
  .stat {
    text-align: center;
    padding: 1rem;
    background: rgba(0, 255, 255, 0.1);
    border-radius: $border-radius-md;
    
    .stat-value {
      display: block;
      font-family: $font-heading;
      font-size: 1.5rem;
      font-weight: 700;
      color: $color-neon-aqua;
      margin-bottom: 0.3rem;
    }
    
    .stat-label {
      font-size: 0.8rem;
      color: $color-text-muted;
      text-transform: uppercase;
    }
  }
}

.solution-cta {
  width: 100%;
  justify-content: center;
  margin-top: auto;
}
</style>
