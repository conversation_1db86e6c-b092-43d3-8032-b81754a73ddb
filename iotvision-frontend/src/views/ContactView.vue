<template>
  <div class="contact-view">
    <section class="contact-hero section">
      <div class="container">
        <div class="hero-content">
          <h1 class="hero-title">
            {{ $t('contact.title') }}
          </h1>
          <p class="hero-subtitle">
            {{ $t('contact.subtitle') }}
          </p>
        </div>
      </div>
    </section>

    <section class="contact-content section">
      <div class="container">
        <div class="contact-grid">
          <div class="contact-form-section">
            <h2>{{ $t('contact.form.send') }}</h2>
            <form @submit.prevent="submitForm" class="contact-form">
              <div class="form-group">
                <label for="name">{{ $t('contact.form.name') }} *</label>
                <input
                  type="text"
                  id="name"
                  v-model="form.name"
                  required
                  class="form-input"
                  :placeholder="$t('contact.form.name')"
                >
              </div>
              
              <div class="form-group">
                <label for="email">{{ $t('contact.form.email') }} *</label>
                <input
                  type="email"
                  id="email"
                  v-model="form.email"
                  required
                  class="form-input"
                  :placeholder="$t('contact.form.email')"
                >
              </div>

              <div class="form-group">
                <label for="company">{{ $t('contact.form.company') }}</label>
                <input
                  type="text"
                  id="company"
                  v-model="form.company"
                  class="form-input"
                  :placeholder="$t('contact.form.company')"
                >
              </div>

              <div class="form-group">
                <label for="phone">{{ $t('contact.form.phone') }}</label>
                <input
                  type="tel"
                  id="phone"
                  v-model="form.phone"
                  class="form-input"
                  :placeholder="$t('contact.form.phone')"
                >
              </div>
              
              <div class="form-group">
                <label for="subject">{{ $t('contact.form.subject') }} *</label>
                <select id="subject" v-model="form.subject" required class="form-select">
                  <option value="">{{ $t('contact.form.subject') }}</option>
                  <option value="general">General Inquiry</option>
                  <option value="demo">Request Demo</option>
                  <option value="partnership">Partnership</option>
                  <option value="support">Technical Support</option>
                  <option value="pricing">Pricing Information</option>
                </select>
              </div>

              <div class="form-group">
                <label for="message">{{ $t('contact.form.message') }} *</label>
                <textarea
                  id="message"
                  v-model="form.message"
                  required
                  class="form-textarea"
                  rows="5"
                  :placeholder="$t('contact.form.message')"
                ></textarea>
              </div>

              <button type="submit" class="btn btn-primary form-submit" :disabled="isSubmitting">
                <span v-if="!isSubmitting">{{ $t('contact.form.send') }}</span>
                <span v-else>{{ $t('contact.form.sending') }}</span>
                <svg v-if="!isSubmitting" class="btn-icon" width="20" height="20" viewBox="0 0 24 24" fill="none">
                  <path d="M22 2L11 13M22 2l-7 20-4-9-9-4 20-7z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </button>

              <div v-if="submitMessage" class="submit-message" :class="submitMessageType">
                {{ submitMessage }}
              </div>
            </form>
          </div>
          
          <div class="contact-info-section">
            <h2>{{ $t('nav.contact') }}</h2>

            <div class="contact-methods">
              <div class="contact-method">
                <div class="method-icon">📧</div>
                <div class="method-content">
                  <h3>Email</h3>
                  <p>{{ $t('contact.info.email') }}</p>
                  <p><EMAIL></p>
                </div>
              </div>

              <div class="contact-method">
                <div class="method-icon">📞</div>
                <div class="method-content">
                  <h3>Phone</h3>
                  <p>{{ $t('contact.info.phone') }}</p>
                  <p>{{ $t('contact.info.hours') }}</p>
                </div>
              </div>

              <div class="contact-method">
                <div class="method-icon">📍</div>
                <div class="method-content">
                  <h3>Address</h3>
                  <p>{{ $t('contact.info.address') }}</p>
                </div>
              </div>
            </div>
            
            <div class="response-time">
              <h3>Response Time</h3>
              <div class="response-stats">
                <div class="response-stat">
                  <span class="stat-value">< 2h</span>
                  <span class="stat-label">Average Response</span>
                </div>
                <div class="response-stat">
                  <span class="stat-value">24/7</span>
                  <span class="stat-label">Emergency Support</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'

const isSubmitting = ref(false)

const form = reactive({
  name: '',
  email: '',
  company: '',
  phone: '',
  subject: '',
  message: ''
})

const submitMessage = ref('')
const submitMessageType = ref('')

const submitForm = async () => {
  isSubmitting.value = true
  submitMessage.value = ''

  try {
    // Simulate form submission
    await new Promise(resolve => setTimeout(resolve, 2000))

    // Reset form
    Object.keys(form).forEach(key => {
      form[key as keyof typeof form] = ''
    })

    submitMessage.value = 'Message sent successfully! We\'ll get back to you soon.'
    submitMessageType.value = 'success'

    // Clear message after 5 seconds
    setTimeout(() => {
      submitMessage.value = ''
    }, 5000)
  } catch (error) {
    submitMessage.value = 'Error sending message. Please try again.'
    submitMessageType.value = 'error'
  } finally {
    isSubmitting.value = false
  }
}
</script>

<style scoped lang="scss">

.contact-view {
  padding-top: 70px;
}

.contact-hero {
  background: linear-gradient(135deg, rgba(26, 26, 46, 0.8) 0%, rgba(10, 10, 15, 0.9) 100%);
  text-align: center;
  padding: 4rem 0;
}

.hero-content {
  max-width: 800px;
  margin: 0 auto;
}

.hero-title {
  font-size: 3rem;
  margin-bottom: 1.5rem;
  
  @media (max-width: $breakpoint-md) {
    font-size: 2.5rem;
  }
}

.hero-subtitle {
  font-size: 1.3rem;
  color: $color-text-muted;
  line-height: 1.6;
}

.contact-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 4rem;
  
  @media (max-width: $breakpoint-lg) {
    grid-template-columns: 1fr;
    gap: 3rem;
  }
}

.contact-form-section {
  h2 {
    font-size: 2rem;
    margin-bottom: 2rem;
    color: $color-text-white;
  }
}

.contact-form {
  .form-group {
    margin-bottom: 1.5rem;
    
    label {
      display: block;
      margin-bottom: 0.5rem;
      color: $color-text-light;
      font-weight: 500;
    }
  }
  
  .form-input,
  .form-select,
  .form-textarea {
    width: 100%;
    padding: 1rem;
    background: rgba(26, 26, 46, 0.6);
    border: 1px solid rgba(0, 255, 255, 0.2);
    border-radius: $border-radius-md;
    color: $color-text-white;
    font-size: 1rem;
    transition: all 0.3s ease;
    
    &:focus {
      border-color: $color-neon-aqua;
      box-shadow: 0 0 0 2px rgba(0, 255, 255, 0.1);
    }
    
    &::placeholder {
      color: $color-text-muted;
    }
  }
  
  .form-textarea {
    resize: vertical;
    min-height: 120px;
  }
  
  .form-submit {
    width: 100%;
    justify-content: center;
    gap: 0.5rem;
    padding: 1rem 2rem;
    font-size: 1.1rem;

    &:disabled {
      opacity: 0.7;
      cursor: not-allowed;
    }
  }

  .submit-message {
    margin-top: 1rem;
    padding: 1rem;
    border-radius: $border-radius-md;
    text-align: center;
    font-weight: 500;

    &.success {
      background: rgba(34, 197, 94, 0.1);
      border: 1px solid rgba(34, 197, 94, 0.3);
      color: #22c55e;
    }

    &.error {
      background: rgba(239, 68, 68, 0.1);
      border: 1px solid rgba(239, 68, 68, 0.3);
      color: #ef4444;
    }
  }
}

.contact-info-section {
  h2 {
    font-size: 2rem;
    margin-bottom: 2rem;
    color: $color-text-white;
  }
}

.contact-methods {
  margin-bottom: 3rem;
}

.contact-method {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: rgba(26, 26, 46, 0.6);
  border: 1px solid rgba(0, 255, 255, 0.2);
  border-radius: $border-radius-lg;
  
  .method-icon {
    font-size: 2rem;
    flex-shrink: 0;
  }
  
  .method-content {
    h3 {
      color: $color-text-white;
      margin-bottom: 0.5rem;
      font-size: 1.1rem;
    }
    
    p {
      color: $color-text-muted;
      margin-bottom: 0.3rem;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

.response-time {
  h3 {
    color: $color-text-white;
    margin-bottom: 1rem;
    font-size: 1.3rem;
  }
}

.response-stats {
  display: flex;
  gap: 1rem;
  
  @media (max-width: $breakpoint-sm) {
    flex-direction: column;
  }
}

.response-stat {
  flex: 1;
  text-align: center;
  padding: 1rem;
  background: rgba(0, 255, 255, 0.1);
  border-radius: $border-radius-md;
  
  .stat-value {
    display: block;
    font-family: $font-heading;
    font-size: 1.5rem;
    font-weight: 700;
    color: $color-neon-aqua;
    margin-bottom: 0.3rem;
  }
  
  .stat-label {
    font-size: 0.8rem;
    color: $color-text-muted;
    text-transform: uppercase;
  }
}
</style>
