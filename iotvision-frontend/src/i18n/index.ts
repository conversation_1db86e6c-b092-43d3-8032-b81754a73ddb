import { createI18n } from 'vue-i18n'

// Import language files
import en from './locales/en.json'
import cs from './locales/cs.json'

// Detect browser language with localStorage preference
const getBrowserLanguage = (): string => {
  // First check localStorage for saved preference
  const savedLang = localStorage.getItem('preferred-language')
  if (savedLang && ['en', 'cs'].includes(savedLang)) {
    return savedLang
  }

  // Then check browser language
  const browserLang = navigator.language || (navigator as any).userLanguage

  // Check if browser language starts with 'cs' (Czech)
  if (browserLang.toLowerCase().startsWith('cs')) {
    return 'cs'
  }

  // Default to English for all other languages
  return 'en'
}

const i18n = createI18n({
  legacy: false,
  locale: getBrowserLanguage(),
  fallbackLocale: 'en',
  messages: {
    en,
    cs
  }
})

export default i18n
