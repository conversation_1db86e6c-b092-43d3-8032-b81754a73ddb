// Animation definitions
@use 'variables' as *;

// Keyframes
@keyframes pulse-neon {
  0%, 100% {
    opacity: 1;
    text-shadow: 0 0 10px currentColor;
  }
  50% {
    opacity: 0.8;
    text-shadow: 0 0 20px currentColor, 0 0 30px currentColor;
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 5px currentColor;
  }
  50% {
    box-shadow: 0 0 20px currentColor, 0 0 30px currentColor;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes slide-in-left {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slide-in-right {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slide-in-up {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slide-in-down {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes scale-in {
  from {
    transform: scale(0.8);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes rotate-360 {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes data-flow {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateX(100%);
    opacity: 0;
  }
}

@keyframes particle-float {
  0%, 100% {
    transform: translate(0, 0) rotate(0deg);
  }
  33% {
    transform: translate(30px, -30px) rotate(120deg);
  }
  66% {
    transform: translate(-20px, 20px) rotate(240deg);
  }
}

// Animation utility classes
.animate-pulse-neon {
  animation: pulse-neon 2s ease-in-out infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-slide-in-left {
  animation: slide-in-left $duration-slow ease-out;
}

.animate-slide-in-right {
  animation: slide-in-right $duration-slow ease-out;
}

.animate-slide-in-up {
  animation: slide-in-up $duration-slow ease-out;
}

.animate-slide-in-down {
  animation: slide-in-down $duration-slow ease-out;
}

.animate-fade-in {
  animation: fade-in $duration-slow ease-out;
}

.animate-scale-in {
  animation: scale-in $duration-slow ease-out;
}

.animate-rotate {
  animation: rotate-360 2s linear infinite;
}

.animate-data-flow {
  animation: data-flow 3s linear infinite;
}

.animate-particle-float {
  animation: particle-float 6s ease-in-out infinite;
}

// Hover effects
.hover-glow {
  transition: all $duration-normal ease;
  
  &:hover {
    box-shadow: 0 0 20px currentColor;
    transform: translateY(-2px);
  }
}

.hover-scale {
  transition: transform $duration-normal ease;
  
  &:hover {
    transform: scale(1.05);
  }
}

.hover-neon {
  transition: all $duration-normal ease;
  
  &:hover {
    color: $color-neon-aqua;
    text-shadow: 0 0 10px currentColor;
  }
}

// Transition utilities
.transition-all {
  transition: all $duration-normal ease;
}

.transition-fast {
  transition: all $duration-fast ease;
}

.transition-slow {
  transition: all $duration-slow ease;
}

// Animation delays
.delay-100 {
  animation-delay: 0.1s;
}

.delay-200 {
  animation-delay: 0.2s;
}

.delay-300 {
  animation-delay: 0.3s;
}

.delay-500 {
  animation-delay: 0.5s;
}
