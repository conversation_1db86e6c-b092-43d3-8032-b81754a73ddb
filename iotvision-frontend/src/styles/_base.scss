// Base styles and CSS reset
@use 'variables' as *;

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: $font-body;
  font-size: $font-size-base;
  line-height: 1.6;
  color: $color-text-light;
  background-color: $color-primary-bg;
  background-image: 
    radial-gradient(circle at 20% 80%, rgba(26, 26, 46, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(44, 44, 62, 0.3) 0%, transparent 50%);
  min-height: 100vh;
  overflow-x: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// Remove default button styles
button {
  border: none;
  background: none;
  cursor: pointer;
  font-family: inherit;
}

// Remove default link styles
a {
  color: inherit;
  text-decoration: none;
}

// Remove default list styles
ul, ol {
  list-style: none;
}

// Remove default input styles
input, textarea, select {
  border: none;
  outline: none;
  font-family: inherit;
  background: transparent;
}

// Image optimization
img {
  max-width: 100%;
  height: auto;
  display: block;
}

// Scrollbar styling
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: $color-dark-blue;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, $color-neon-aqua, $color-neon-green);
  border-radius: $border-radius-full;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, $color-neon-green, $color-neon-fuchsia);
}

// Selection styling
::selection {
  background: rgba(0, 255, 255, 0.2);
  color: $color-text-white;
}

::-moz-selection {
  background: rgba(0, 255, 255, 0.2);
  color: $color-text-white;
}

// Focus styles for accessibility
:focus-visible {
  outline: 2px solid $color-neon-aqua;
  outline-offset: 2px;
}

// Layout stability for language switching
.section {
  min-height: 400px; // Prevent layout shifts

  &.hero {
    min-height: 100vh;
  }
}

// Container with consistent padding
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;

  @media (min-width: $breakpoint-md) {
    padding: 0 2rem;
  }

  @media (min-width: $breakpoint-lg) {
    padding: 0;
  }
}

// Consistent button heights to prevent layout shifts
.btn {
  min-height: 48px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
}

// Reduced motion for accessibility
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}
