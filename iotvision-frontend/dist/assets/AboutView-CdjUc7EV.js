import{d as c,k as o,l as s,v as r,t,F as l,y as v,p as n}from"./vendor-Cao0-QMi.js";import{_ as u}from"./index-nRGPD0fz.js";import"./i18n-laVgjn-G.js";const m={class:"about-view"},h={class:"about-hero section"},p={class:"container"},b={class:"hero-content"},_={class:"hero-title"},g={class:"hero-subtitle"},y={class:"about-mission section"},f={class:"container"},C={class:"mission-grid"},w={class:"mission-item"},I={class:"mission-item"},T={class:"about-team section"},V={class:"container"},S={class:"team-grid"},$={class:"member-avatar"},A={class:"member-name"},F={class:"member-role"},M={class:"member-bio"},O=c({__name:"<PERSON><PERSON>iew",setup(k){const d=[{id:1,name:"<PERSON>",role:"CEO & Co-Founder",avatar:"👨‍💼",bio:"Visionary leader with 15+ years in IoT and enterprise technology."},{id:2,name:"<PERSON>",role:"CTO & Co-Founder",avatar:"👩‍💻",bio:"Technical architect specializing in scalable IoT infrastructure."},{id:3,name:"Michael <PERSON>",role:"Head of AI/ML",avatar:"👨‍🔬",bio:"AI researcher focused on intelligent edge computing solutions."},{id:4,name:"Emily Zhang",role:"Head of Security",avatar:"👩‍🔒",bio:"Cybersecurity expert ensuring enterprise-grade protection."}];return(i,a)=>(n(),o("div",m,[s("section",h,[s("div",p,[s("div",b,[s("h1",_,t(i.$t("about.title")),1),s("p",g,t(i.$t("about.subtitle")),1)])])]),a[4]||(a[4]=r('<section class="about-story section" data-v-1238a1ca><div class="container" data-v-1238a1ca><div class="story-content" data-v-1238a1ca><div class="story-text" data-v-1238a1ca><h2 data-v-1238a1ca>Our Story</h2><p data-v-1238a1ca> Founded in 2020 by a team of visionary engineers and data scientists, IoTVision emerged from a simple yet powerful belief: that the future belongs to those who can seamlessly connect the physical and digital worlds. </p><p data-v-1238a1ca> Today, we&#39;re proud to be at the forefront of the IoT revolution, helping businesses across industries transform their operations through intelligent connectivity and data-driven insights. </p></div><div class="story-stats" data-v-1238a1ca><div class="stat-card" data-v-1238a1ca><div class="stat-number" data-v-1238a1ca>500+</div><div class="stat-label" data-v-1238a1ca>Projects Completed</div></div><div class="stat-card" data-v-1238a1ca><div class="stat-number" data-v-1238a1ca>50M+</div><div class="stat-label" data-v-1238a1ca>Devices Connected</div></div><div class="stat-card" data-v-1238a1ca><div class="stat-number" data-v-1238a1ca>25+</div><div class="stat-label" data-v-1238a1ca>Countries Served</div></div></div></div></div></section>',1)),s("section",y,[s("div",f,[s("div",C,[s("div",w,[a[0]||(a[0]=s("div",{class:"mission-icon"},"🎯",-1)),s("h3",null,t(i.$t("about.mission.title")),1),s("p",null,t(i.$t("about.mission.description")),1)]),s("div",I,[a[1]||(a[1]=s("div",{class:"mission-icon"},"👁️",-1)),s("h3",null,t(i.$t("about.vision.title")),1),s("p",null,t(i.$t("about.vision.description")),1)]),a[2]||(a[2]=s("div",{class:"mission-item"},[s("div",{class:"mission-icon"},"⚡"),s("h3",null,"Our Values"),s("p",null," Innovation, reliability, and customer success drive everything we do. We believe in building lasting partnerships and delivering exceptional value. ")],-1))])])]),s("section",T,[s("div",V,[a[3]||(a[3]=s("h2",{class:"section-title"},"Meet Our Team",-1)),s("div",S,[(n(),o(l,null,v(d,e=>s("div",{key:e.id,class:"team-card"},[s("div",$,t(e.avatar),1),s("h3",A,t(e.name),1),s("p",F,t(e.role),1),s("p",M,t(e.bio),1)])),64))])])])]))}}),D=u(O,[["__scopeId","data-v-1238a1ca"]]);export{D as default};
