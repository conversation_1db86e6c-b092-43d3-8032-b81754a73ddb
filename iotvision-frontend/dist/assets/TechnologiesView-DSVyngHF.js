import{d as h,r as m,c as v,k as n,l as e,t as s,A as _,F as c,y as l,n as g,p as a}from"./vendor-Cao0-QMi.js";import{_ as y}from"./index-nRGPD0fz.js";import"./i18n-laVgjn-G.js";const b={class:"technologies-view"},f={class:"tech-hero section"},k={class:"container"},A={class:"hero-content"},w={class:"hero-title"},C={class:"hero-subtitle"},S={class:"tech-details section"},E={class:"container"},L={class:"tech-tabs"},M=["onClick"],R={key:0,class:"tech-content"},B={class:"tech-overview"},T={class:"tech-info"},I={class:"tech-title"},N={class:"tech-description"},O={class:"tech-benefits"},P={class:"tech-visual"},F={class:"visual-placeholder"},V={class:"tech-icon"},D={class:"tech-stats"},$={class:"stat-value"},z={class:"stat-label"},G={class:"tech-features"},U={class:"features-grid"},W={class:"feature-specs"},x=h({__name:"TechnologiesView",setup(K){const d=m(0),u=[{id:1,name:"Connectivity",icon:"📡",description:"Advanced multi-protocol connectivity solutions ensuring seamless communication between devices across various networks and environments.",benefits:["Multi-protocol support (WiFi 6, 5G, LoRaWAN, Bluetooth 5.0)","Extended range up to 10km","Ultra-low latency (<1ms)","Automatic failover and redundancy"],stats:[{label:"Protocols",value:"15+"},{label:"Range",value:"10km"},{label:"Latency",value:"<1ms"}],features:[{name:"Mesh Networking",description:"Self-healing network topology that automatically routes around failures",specs:["Auto-discovery","Load balancing","Redundancy"]},{name:"Edge Computing",description:"Process data locally to reduce latency and bandwidth usage",specs:["Real-time processing","Local storage","Offline capability"]}]},{id:2,name:"AI & Analytics",icon:"🧠",description:"Intelligent data processing with machine learning algorithms that transform raw sensor data into actionable business insights.",benefits:["Real-time anomaly detection","Predictive maintenance capabilities","Automated decision making","99.5% accuracy in pattern recognition"],stats:[{label:"Models",value:"50+"},{label:"Accuracy",value:"99.5%"},{label:"Processing",value:"Real-time"}],features:[{name:"Machine Learning",description:"Advanced ML models for pattern recognition and prediction",specs:["Deep learning","Neural networks","Auto-training"]},{name:"Data Analytics",description:"Comprehensive analytics platform for business intelligence",specs:["Real-time dashboards","Custom reports","API access"]}]},{id:3,name:"Security",icon:"🔒",description:"Enterprise-grade security framework with end-to-end encryption, zero-trust architecture, and compliance with international standards.",benefits:["End-to-end AES-256 encryption","Zero-trust security model","SOC2 and ISO27001 compliance","Multi-factor authentication"],stats:[{label:"Encryption",value:"AES-256"},{label:"Compliance",value:"SOC2"},{label:"Uptime",value:"99.99%"}],features:[{name:"Encryption",description:"Military-grade encryption for all data transmission",specs:["AES-256","TLS 1.3","Key rotation"]},{name:"Access Control",description:"Granular permissions and role-based access control",specs:["RBAC","SSO","MFA"]}]}],i=v(()=>u[d.value]);return(p,r)=>(a(),n("div",b,[e("section",f,[e("div",k,[e("div",A,[e("h1",w,s(p.$t("technologies.title")),1),e("p",C,s(p.$t("technologies.subtitle")),1)])])]),e("section",S,[e("div",E,[e("div",L,[(a(),n(c,null,l(u,(t,o)=>e("button",{key:t.id,class:g(["tab-button",{active:d.value===o}]),onClick:Z=>d.value=o},s(t.name),11,M)),64))]),i.value?(a(),n("div",R,[e("div",B,[e("div",T,[e("h2",I,s(i.value.name),1),e("p",N,s(i.value.description),1),e("div",O,[r[0]||(r[0]=e("h3",null,"Benefits",-1)),e("ul",null,[(a(!0),n(c,null,l(i.value.benefits,t=>(a(),n("li",{key:t},s(t),1))),128))])])]),e("div",P,[e("div",F,[e("div",V,s(i.value.icon),1),e("div",D,[(a(!0),n(c,null,l(i.value.stats,t=>(a(),n("div",{key:t.label,class:"stat-item"},[e("span",$,s(t.value),1),e("span",z,s(t.label),1)]))),128))])])])]),e("div",G,[r[1]||(r[1]=e("h3",null,"Technical Specifications",-1)),e("div",U,[(a(!0),n(c,null,l(i.value.features,t=>(a(),n("div",{key:t.name,class:"feature-card"},[e("h4",null,s(t.name),1),e("p",null,s(t.description),1),e("div",W,[(a(!0),n(c,null,l(t.specs,o=>(a(),n("span",{key:o,class:"spec-tag"},s(o),1))),128))])]))),128))])])])):_("",!0)])])]))}}),J=y(x,[["__scopeId","data-v-f2c4ce3e"]]);export{J as default};
