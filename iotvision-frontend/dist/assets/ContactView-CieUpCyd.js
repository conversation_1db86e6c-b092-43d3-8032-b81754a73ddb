import{d as _,r as u,G as b,k as c,l as t,t as e,H as g,A as v,I as i,J as r,K as y,v as h,n as $,p as m}from"./vendor-Cao0-QMi.js";import{_ as k}from"./index-nRGPD0fz.js";import"./i18n-laVgjn-G.js";const w={class:"contact-view"},V={class:"contact-hero section"},j={class:"container"},M={class:"hero-content"},S={class:"hero-title"},q={class:"hero-subtitle"},C={class:"contact-content section"},T={class:"container"},U={class:"contact-grid"},E={class:"contact-form-section"},P={class:"form-group"},B={for:"name"},I=["placeholder"],A={class:"form-group"},D={for:"email"},N=["placeholder"],R={class:"form-group"},z={for:"company"},G=["placeholder"],F={class:"form-group"},H={for:"phone"},J=["placeholder"],K={class:"form-group"},L={for:"subject"},O={value:""},W={class:"form-group"},Q={for:"message"},X=["placeholder"],Y=["disabled"],Z={key:0},x={key:1},tt={key:2,class:"btn-icon",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none"},ot={class:"contact-info-section"},st={class:"contact-methods"},et={class:"contact-method"},at={class:"method-content"},nt={class:"contact-method"},lt={class:"method-content"},it={class:"contact-method"},dt={class:"method-content"},ct=_({__name:"ContactView",setup(rt){const d=u(!1),a=b({name:"",email:"",company:"",phone:"",subject:"",message:""}),l=u(""),p=u(""),f=async()=>{d.value=!0,l.value="";try{await new Promise(s=>setTimeout(s,2e3)),Object.keys(a).forEach(s=>{a[s]=""}),l.value="Message sent successfully! We'll get back to you soon.",p.value="success",setTimeout(()=>{l.value=""},5e3)}catch{l.value="Error sending message. Please try again.",p.value="error"}finally{d.value=!1}};return(s,o)=>(m(),c("div",w,[t("section",V,[t("div",j,[t("div",M,[t("h1",S,e(s.$t("contact.title")),1),t("p",q,e(s.$t("contact.subtitle")),1)])])]),t("section",C,[t("div",T,[t("div",U,[t("div",E,[t("h2",null,e(s.$t("contact.form.send")),1),t("form",{onSubmit:g(f,["prevent"]),class:"contact-form"},[t("div",P,[t("label",B,e(s.$t("contact.form.name"))+" *",1),i(t("input",{type:"text",id:"name","onUpdate:modelValue":o[0]||(o[0]=n=>a.name=n),required:"",class:"form-input",placeholder:s.$t("contact.form.name")},null,8,I),[[r,a.name]])]),t("div",A,[t("label",D,e(s.$t("contact.form.email"))+" *",1),i(t("input",{type:"email",id:"email","onUpdate:modelValue":o[1]||(o[1]=n=>a.email=n),required:"",class:"form-input",placeholder:s.$t("contact.form.email")},null,8,N),[[r,a.email]])]),t("div",R,[t("label",z,e(s.$t("contact.form.company")),1),i(t("input",{type:"text",id:"company","onUpdate:modelValue":o[2]||(o[2]=n=>a.company=n),class:"form-input",placeholder:s.$t("contact.form.company")},null,8,G),[[r,a.company]])]),t("div",F,[t("label",H,e(s.$t("contact.form.phone")),1),i(t("input",{type:"tel",id:"phone","onUpdate:modelValue":o[3]||(o[3]=n=>a.phone=n),class:"form-input",placeholder:s.$t("contact.form.phone")},null,8,J),[[r,a.phone]])]),t("div",K,[t("label",L,e(s.$t("contact.form.subject"))+" *",1),i(t("select",{id:"subject","onUpdate:modelValue":o[4]||(o[4]=n=>a.subject=n),required:"",class:"form-select"},[t("option",O,e(s.$t("contact.form.subject")),1),o[6]||(o[6]=h('<option value="general" data-v-a1a99898>General Inquiry</option><option value="demo" data-v-a1a99898>Request Demo</option><option value="partnership" data-v-a1a99898>Partnership</option><option value="support" data-v-a1a99898>Technical Support</option><option value="pricing" data-v-a1a99898>Pricing Information</option>',5))],512),[[y,a.subject]])]),t("div",W,[t("label",Q,e(s.$t("contact.form.message"))+" *",1),i(t("textarea",{id:"message","onUpdate:modelValue":o[5]||(o[5]=n=>a.message=n),required:"",class:"form-textarea",rows:"5",placeholder:s.$t("contact.form.message")},null,8,X),[[r,a.message]])]),t("button",{type:"submit",class:"btn btn-primary form-submit",disabled:d.value},[d.value?(m(),c("span",x,e(s.$t("contact.form.sending")),1)):(m(),c("span",Z,e(s.$t("contact.form.send")),1)),d.value?v("",!0):(m(),c("svg",tt,o[7]||(o[7]=[t("path",{d:"M22 2L11 13M22 2l-7 20-4-9-9-4 20-7z",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1)])))],8,Y),l.value?(m(),c("div",{key:0,class:$(["submit-message",p.value])},e(l.value),3)):v("",!0)],32)]),t("div",ot,[t("h2",null,e(s.$t("nav.contact")),1),t("div",st,[t("div",et,[o[10]||(o[10]=t("div",{class:"method-icon"},"📧",-1)),t("div",at,[o[8]||(o[8]=t("h3",null,"Email",-1)),t("p",null,e(s.$t("contact.info.email")),1),o[9]||(o[9]=t("p",null,"<EMAIL>",-1))])]),t("div",nt,[o[12]||(o[12]=t("div",{class:"method-icon"},"📞",-1)),t("div",lt,[o[11]||(o[11]=t("h3",null,"Phone",-1)),t("p",null,e(s.$t("contact.info.phone")),1),t("p",null,e(s.$t("contact.info.hours")),1)])]),t("div",it,[o[14]||(o[14]=t("div",{class:"method-icon"},"📍",-1)),t("div",dt,[o[13]||(o[13]=t("h3",null,"Address",-1)),t("p",null,e(s.$t("contact.info.address")),1)])])]),o[15]||(o[15]=h('<div class="response-time" data-v-a1a99898><h3 data-v-a1a99898>Response Time</h3><div class="response-stats" data-v-a1a99898><div class="response-stat" data-v-a1a99898><span class="stat-value" data-v-a1a99898>&lt; 2h</span><span class="stat-label" data-v-a1a99898>Average Response</span></div><div class="response-stat" data-v-a1a99898><span class="stat-value" data-v-a1a99898>24/7</span><span class="stat-label" data-v-a1a99898>Emergency Support</span></div></div></div>',1))])])])])]))}}),vt=k(ct,[["__scopeId","data-v-a1a99898"]]);export{vt as default};
