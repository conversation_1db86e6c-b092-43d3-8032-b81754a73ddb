/**
* @vue/shared v3.5.16
* (c) 2018-present <PERSON><PERSON> (<PERSON>) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function is(e){const t=Object.create(null);for(const n of e.split(","))t[n]=1;return n=>n in t}const Q={},vt=[],De=()=>{},ki=()=>!1,xn=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),os=e=>e.startsWith("onUpdate:"),ce=Object.assign,ls=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},Wi=Object.prototype.hasOwnProperty,W=(e,t)=>Wi.call(e,t),j=Array.isA<PERSON><PERSON>,bt=e=>Qt(e)==="[object Map]",Sn=e=>Qt(e)==="[object Set]",As=e=>Qt(e)==="[object Date]",L=e=>typeof e=="function",te=e=>typeof e=="string",He=e=>typeof e=="symbol",Z=e=>e!==null&&typeof e=="object",Sr=e=>(Z(e)||L(e))&&L(e.then)&&L(e.catch),Er=Object.prototype.toString,Qt=e=>Er.call(e),qi=e=>Qt(e).slice(8,-1),wr=e=>Qt(e)==="[object Object]",cs=e=>te(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,Dt=is(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),En=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},Gi=/-(\w)/g,st=En(e=>e.replace(Gi,(t,n)=>n?n.toUpperCase():"")),zi=/\B([A-Z])/g,ht=En(e=>e.replace(zi,"-$1").toLowerCase()),Rr=En(e=>e.charAt(0).toUpperCase()+e.slice(1)),Nn=En(e=>e?`on${Rr(e)}`:""),nt=(e,t)=>!Object.is(e,t),sn=(e,...t)=>{for(let n=0;n<e.length;n++)e[n](...t)},Cr=(e,t,n,s=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:s,value:n})},un=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let Ts;const wn=()=>Ts||(Ts=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function fs(e){if(j(e)){const t={};for(let n=0;n<e.length;n++){const s=e[n],r=te(s)?Xi(s):fs(s);if(r)for(const i in r)t[i]=r[i]}return t}else if(te(e)||Z(e))return e}const Ji=/;(?![^(]*\))/g,Qi=/:([^]+)/,Yi=/\/\*[^]*?\*\//g;function Xi(e){const t={};return e.replace(Yi,"").split(Ji).forEach(n=>{if(n){const s=n.split(Qi);s.length>1&&(t[s[0].trim()]=s[1].trim())}}),t}function us(e){let t="";if(te(e))t=e;else if(j(e))for(let n=0;n<e.length;n++){const s=us(e[n]);s&&(t+=s+" ")}else if(Z(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const Zi="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",eo=is(Zi);function Pr(e){return!!e||e===""}function to(e,t){if(e.length!==t.length)return!1;let n=!0;for(let s=0;n&&s<e.length;s++)n=Rn(e[s],t[s]);return n}function Rn(e,t){if(e===t)return!0;let n=As(e),s=As(t);if(n||s)return n&&s?e.getTime()===t.getTime():!1;if(n=He(e),s=He(t),n||s)return e===t;if(n=j(e),s=j(t),n||s)return n&&s?to(e,t):!1;if(n=Z(e),s=Z(t),n||s){if(!n||!s)return!1;const r=Object.keys(e).length,i=Object.keys(t).length;if(r!==i)return!1;for(const o in e){const l=e.hasOwnProperty(o),c=t.hasOwnProperty(o);if(l&&!c||!l&&c||!Rn(e[o],t[o]))return!1}}return String(e)===String(t)}function no(e,t){return e.findIndex(n=>Rn(n,t))}const Ar=e=>!!(e&&e.__v_isRef===!0),so=e=>te(e)?e:e==null?"":j(e)||Z(e)&&(e.toString===Er||!L(e.toString))?Ar(e)?so(e.value):JSON.stringify(e,Tr,2):String(e),Tr=(e,t)=>Ar(t)?Tr(e,t.value):bt(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((n,[s,r],i)=>(n[Dn(s,i)+" =>"]=r,n),{})}:Sn(t)?{[`Set(${t.size})`]:[...t.values()].map(n=>Dn(n))}:He(t)?Dn(t):Z(t)&&!j(t)&&!wr(t)?String(t):t,Dn=(e,t="")=>{var n;return He(e)?`Symbol(${(n=e.description)!=null?n:t})`:e};/**
* @vue/reactivity v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let de;class Or{constructor(t=!1){this.detached=t,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=de,!t&&de&&(this.index=(de.scopes||(de.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].pause();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].resume();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].resume()}}run(t){if(this._active){const n=de;try{return de=this,t()}finally{de=n}}}on(){++this._on===1&&(this.prevScope=de,de=this)}off(){this._on>0&&--this._on===0&&(de=this.prevScope,this.prevScope=void 0)}stop(t){if(this._active){this._active=!1;let n,s;for(n=0,s=this.effects.length;n<s;n++)this.effects[n].stop();for(this.effects.length=0,n=0,s=this.cleanups.length;n<s;n++)this.cleanups[n]();if(this.cleanups.length=0,this.scopes){for(n=0,s=this.scopes.length;n<s;n++)this.scopes[n].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const r=this.parent.scopes.pop();r&&r!==this&&(this.parent.scopes[this.index]=r,r.index=this.index)}this.parent=void 0}}}function ro(e){return new Or(e)}function io(){return de}let X;const Hn=new WeakSet;class Mr{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,de&&de.active&&de.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,Hn.has(this)&&(Hn.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||Fr(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,Os(this),Nr(this);const t=X,n=Ee;X=this,Ee=!0;try{return this.fn()}finally{Dr(this),X=t,Ee=n,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)ds(t);this.deps=this.depsTail=void 0,Os(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?Hn.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){qn(this)&&this.run()}get dirty(){return qn(this)}}let Ir=0,Ht,jt;function Fr(e,t=!1){if(e.flags|=8,t){e.next=jt,jt=e;return}e.next=Ht,Ht=e}function as(){Ir++}function hs(){if(--Ir>0)return;if(jt){let t=jt;for(jt=void 0;t;){const n=t.next;t.next=void 0,t.flags&=-9,t=n}}let e;for(;Ht;){let t=Ht;for(Ht=void 0;t;){const n=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(s){e||(e=s)}t=n}}if(e)throw e}function Nr(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function Dr(e){let t,n=e.depsTail,s=n;for(;s;){const r=s.prevDep;s.version===-1?(s===n&&(n=r),ds(s),oo(s)):t=s,s.dep.activeLink=s.prevActiveLink,s.prevActiveLink=void 0,s=r}e.deps=t,e.depsTail=n}function qn(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(Hr(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function Hr(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===Ut)||(e.globalVersion=Ut,!e.isSSR&&e.flags&128&&(!e.deps&&!e._dirty||!qn(e))))return;e.flags|=2;const t=e.dep,n=X,s=Ee;X=e,Ee=!0;try{Nr(e);const r=e.fn(e._value);(t.version===0||nt(r,e._value))&&(e.flags|=128,e._value=r,t.version++)}catch(r){throw t.version++,r}finally{X=n,Ee=s,Dr(e),e.flags&=-3}}function ds(e,t=!1){const{dep:n,prevSub:s,nextSub:r}=e;if(s&&(s.nextSub=r,e.prevSub=void 0),r&&(r.prevSub=s,e.nextSub=void 0),n.subs===e&&(n.subs=s,!s&&n.computed)){n.computed.flags&=-5;for(let i=n.computed.deps;i;i=i.nextDep)ds(i,!0)}!t&&!--n.sc&&n.map&&n.map.delete(n.key)}function oo(e){const{prevDep:t,nextDep:n}=e;t&&(t.nextDep=n,e.prevDep=void 0),n&&(n.prevDep=t,e.nextDep=void 0)}let Ee=!0;const jr=[];function qe(){jr.push(Ee),Ee=!1}function Ge(){const e=jr.pop();Ee=e===void 0?!0:e}function Os(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const n=X;X=void 0;try{t()}finally{X=n}}}let Ut=0;class lo{constructor(t,n){this.sub=t,this.dep=n,this.version=n.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class ps{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0}track(t){if(!X||!Ee||X===this.computed)return;let n=this.activeLink;if(n===void 0||n.sub!==X)n=this.activeLink=new lo(X,this),X.deps?(n.prevDep=X.depsTail,X.depsTail.nextDep=n,X.depsTail=n):X.deps=X.depsTail=n,$r(n);else if(n.version===-1&&(n.version=this.version,n.nextDep)){const s=n.nextDep;s.prevDep=n.prevDep,n.prevDep&&(n.prevDep.nextDep=s),n.prevDep=X.depsTail,n.nextDep=void 0,X.depsTail.nextDep=n,X.depsTail=n,X.deps===n&&(X.deps=s)}return n}trigger(t){this.version++,Ut++,this.notify(t)}notify(t){as();try{for(let n=this.subs;n;n=n.prevSub)n.sub.notify()&&n.sub.dep.notify()}finally{hs()}}}function $r(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let s=t.deps;s;s=s.nextDep)$r(s)}const n=e.dep.subs;n!==e&&(e.prevSub=n,n&&(n.nextSub=e)),e.dep.subs=e}}const Gn=new WeakMap,at=Symbol(""),zn=Symbol(""),kt=Symbol("");function ie(e,t,n){if(Ee&&X){let s=Gn.get(e);s||Gn.set(e,s=new Map);let r=s.get(n);r||(s.set(n,r=new ps),r.map=s,r.key=n),r.track()}}function Ue(e,t,n,s,r,i){const o=Gn.get(e);if(!o){Ut++;return}const l=c=>{c&&c.trigger()};if(as(),t==="clear")o.forEach(l);else{const c=j(e),d=c&&cs(n);if(c&&n==="length"){const a=Number(s);o.forEach((h,g)=>{(g==="length"||g===kt||!He(g)&&g>=a)&&l(h)})}else switch((n!==void 0||o.has(void 0))&&l(o.get(n)),d&&l(o.get(kt)),t){case"add":c?d&&l(o.get("length")):(l(o.get(at)),bt(e)&&l(o.get(zn)));break;case"delete":c||(l(o.get(at)),bt(e)&&l(o.get(zn)));break;case"set":bt(e)&&l(o.get(at));break}}hs()}function mt(e){const t=k(e);return t===e?t:(ie(t,"iterate",kt),xe(e)?t:t.map(re))}function Cn(e){return ie(e=k(e),"iterate",kt),e}const co={__proto__:null,[Symbol.iterator](){return jn(this,Symbol.iterator,re)},concat(...e){return mt(this).concat(...e.map(t=>j(t)?mt(t):t))},entries(){return jn(this,"entries",e=>(e[1]=re(e[1]),e))},every(e,t){return Le(this,"every",e,t,void 0,arguments)},filter(e,t){return Le(this,"filter",e,t,n=>n.map(re),arguments)},find(e,t){return Le(this,"find",e,t,re,arguments)},findIndex(e,t){return Le(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return Le(this,"findLast",e,t,re,arguments)},findLastIndex(e,t){return Le(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return Le(this,"forEach",e,t,void 0,arguments)},includes(...e){return $n(this,"includes",e)},indexOf(...e){return $n(this,"indexOf",e)},join(e){return mt(this).join(e)},lastIndexOf(...e){return $n(this,"lastIndexOf",e)},map(e,t){return Le(this,"map",e,t,void 0,arguments)},pop(){return Mt(this,"pop")},push(...e){return Mt(this,"push",e)},reduce(e,...t){return Ms(this,"reduce",e,t)},reduceRight(e,...t){return Ms(this,"reduceRight",e,t)},shift(){return Mt(this,"shift")},some(e,t){return Le(this,"some",e,t,void 0,arguments)},splice(...e){return Mt(this,"splice",e)},toReversed(){return mt(this).toReversed()},toSorted(e){return mt(this).toSorted(e)},toSpliced(...e){return mt(this).toSpliced(...e)},unshift(...e){return Mt(this,"unshift",e)},values(){return jn(this,"values",re)}};function jn(e,t,n){const s=Cn(e),r=s[t]();return s!==e&&!xe(e)&&(r._next=r.next,r.next=()=>{const i=r._next();return i.value&&(i.value=n(i.value)),i}),r}const fo=Array.prototype;function Le(e,t,n,s,r,i){const o=Cn(e),l=o!==e&&!xe(e),c=o[t];if(c!==fo[t]){const h=c.apply(e,i);return l?re(h):h}let d=n;o!==e&&(l?d=function(h,g){return n.call(this,re(h),g,e)}:n.length>2&&(d=function(h,g){return n.call(this,h,g,e)}));const a=c.call(o,d,s);return l&&r?r(a):a}function Ms(e,t,n,s){const r=Cn(e);let i=n;return r!==e&&(xe(e)?n.length>3&&(i=function(o,l,c){return n.call(this,o,l,c,e)}):i=function(o,l,c){return n.call(this,o,re(l),c,e)}),r[t](i,...s)}function $n(e,t,n){const s=k(e);ie(s,"iterate",kt);const r=s[t](...n);return(r===-1||r===!1)&&_s(n[0])?(n[0]=k(n[0]),s[t](...n)):r}function Mt(e,t,n=[]){qe(),as();const s=k(e)[t].apply(e,n);return hs(),Ge(),s}const uo=is("__proto__,__v_isRef,__isVue"),Lr=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(He));function ao(e){He(e)||(e=String(e));const t=k(this);return ie(t,"has",e),t.hasOwnProperty(e)}class Vr{constructor(t=!1,n=!1){this._isReadonly=t,this._isShallow=n}get(t,n,s){if(n==="__v_skip")return t.__v_skip;const r=this._isReadonly,i=this._isShallow;if(n==="__v_isReactive")return!r;if(n==="__v_isReadonly")return r;if(n==="__v_isShallow")return i;if(n==="__v_raw")return s===(r?i?So:kr:i?Ur:Br).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(s)?t:void 0;const o=j(t);if(!r){let c;if(o&&(c=co[n]))return c;if(n==="hasOwnProperty")return ao}const l=Reflect.get(t,n,le(t)?t:s);return(He(n)?Lr.has(n):uo(n))||(r||ie(t,"get",n),i)?l:le(l)?o&&cs(n)?l:l.value:Z(l)?r?qr(l):Pn(l):l}}class Kr extends Vr{constructor(t=!1){super(!1,t)}set(t,n,s,r){let i=t[n];if(!this._isShallow){const c=rt(i);if(!xe(s)&&!rt(s)&&(i=k(i),s=k(s)),!j(t)&&le(i)&&!le(s))return c?!1:(i.value=s,!0)}const o=j(t)&&cs(n)?Number(n)<t.length:W(t,n),l=Reflect.set(t,n,s,le(t)?t:r);return t===k(r)&&(o?nt(s,i)&&Ue(t,"set",n,s):Ue(t,"add",n,s)),l}deleteProperty(t,n){const s=W(t,n);t[n];const r=Reflect.deleteProperty(t,n);return r&&s&&Ue(t,"delete",n,void 0),r}has(t,n){const s=Reflect.has(t,n);return(!He(n)||!Lr.has(n))&&ie(t,"has",n),s}ownKeys(t){return ie(t,"iterate",j(t)?"length":at),Reflect.ownKeys(t)}}class ho extends Vr{constructor(t=!1){super(!0,t)}set(t,n){return!0}deleteProperty(t,n){return!0}}const po=new Kr,go=new ho,mo=new Kr(!0);const Jn=e=>e,en=e=>Reflect.getPrototypeOf(e);function _o(e,t,n){return function(...s){const r=this.__v_raw,i=k(r),o=bt(i),l=e==="entries"||e===Symbol.iterator&&o,c=e==="keys"&&o,d=r[e](...s),a=n?Jn:t?an:re;return!t&&ie(i,"iterate",c?zn:at),{next(){const{value:h,done:g}=d.next();return g?{value:h,done:g}:{value:l?[a(h[0]),a(h[1])]:a(h),done:g}},[Symbol.iterator](){return this}}}}function tn(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function yo(e,t){const n={get(r){const i=this.__v_raw,o=k(i),l=k(r);e||(nt(r,l)&&ie(o,"get",r),ie(o,"get",l));const{has:c}=en(o),d=t?Jn:e?an:re;if(c.call(o,r))return d(i.get(r));if(c.call(o,l))return d(i.get(l));i!==o&&i.get(r)},get size(){const r=this.__v_raw;return!e&&ie(k(r),"iterate",at),Reflect.get(r,"size",r)},has(r){const i=this.__v_raw,o=k(i),l=k(r);return e||(nt(r,l)&&ie(o,"has",r),ie(o,"has",l)),r===l?i.has(r):i.has(r)||i.has(l)},forEach(r,i){const o=this,l=o.__v_raw,c=k(l),d=t?Jn:e?an:re;return!e&&ie(c,"iterate",at),l.forEach((a,h)=>r.call(i,d(a),d(h),o))}};return ce(n,e?{add:tn("add"),set:tn("set"),delete:tn("delete"),clear:tn("clear")}:{add(r){!t&&!xe(r)&&!rt(r)&&(r=k(r));const i=k(this);return en(i).has.call(i,r)||(i.add(r),Ue(i,"add",r,r)),this},set(r,i){!t&&!xe(i)&&!rt(i)&&(i=k(i));const o=k(this),{has:l,get:c}=en(o);let d=l.call(o,r);d||(r=k(r),d=l.call(o,r));const a=c.call(o,r);return o.set(r,i),d?nt(i,a)&&Ue(o,"set",r,i):Ue(o,"add",r,i),this},delete(r){const i=k(this),{has:o,get:l}=en(i);let c=o.call(i,r);c||(r=k(r),c=o.call(i,r)),l&&l.call(i,r);const d=i.delete(r);return c&&Ue(i,"delete",r,void 0),d},clear(){const r=k(this),i=r.size!==0,o=r.clear();return i&&Ue(r,"clear",void 0,void 0),o}}),["keys","values","entries",Symbol.iterator].forEach(r=>{n[r]=_o(r,e,t)}),n}function gs(e,t){const n=yo(e,t);return(s,r,i)=>r==="__v_isReactive"?!e:r==="__v_isReadonly"?e:r==="__v_raw"?s:Reflect.get(W(n,r)&&r in s?n:s,r,i)}const vo={get:gs(!1,!1)},bo={get:gs(!1,!0)},xo={get:gs(!0,!1)};const Br=new WeakMap,Ur=new WeakMap,kr=new WeakMap,So=new WeakMap;function Eo(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function wo(e){return e.__v_skip||!Object.isExtensible(e)?0:Eo(qi(e))}function Pn(e){return rt(e)?e:ms(e,!1,po,vo,Br)}function Wr(e){return ms(e,!1,mo,bo,Ur)}function qr(e){return ms(e,!0,go,xo,kr)}function ms(e,t,n,s,r){if(!Z(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const i=wo(e);if(i===0)return e;const o=r.get(e);if(o)return o;const l=new Proxy(e,i===2?s:n);return r.set(e,l),l}function xt(e){return rt(e)?xt(e.__v_raw):!!(e&&e.__v_isReactive)}function rt(e){return!!(e&&e.__v_isReadonly)}function xe(e){return!!(e&&e.__v_isShallow)}function _s(e){return e?!!e.__v_raw:!1}function k(e){const t=e&&e.__v_raw;return t?k(t):e}function Gr(e){return!W(e,"__v_skip")&&Object.isExtensible(e)&&Cr(e,"__v_skip",!0),e}const re=e=>Z(e)?Pn(e):e,an=e=>Z(e)?qr(e):e;function le(e){return e?e.__v_isRef===!0:!1}function zr(e){return Jr(e,!1)}function Ro(e){return Jr(e,!0)}function Jr(e,t){return le(e)?e:new Co(e,t)}class Co{constructor(t,n){this.dep=new ps,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=n?t:k(t),this._value=n?t:re(t),this.__v_isShallow=n}get value(){return this.dep.track(),this._value}set value(t){const n=this._rawValue,s=this.__v_isShallow||xe(t)||rt(t);t=s?t:k(t),nt(t,n)&&(this._rawValue=t,this._value=s?t:re(t),this.dep.trigger())}}function St(e){return le(e)?e.value:e}const Po={get:(e,t,n)=>t==="__v_raw"?e:St(Reflect.get(e,t,n)),set:(e,t,n,s)=>{const r=e[t];return le(r)&&!le(n)?(r.value=n,!0):Reflect.set(e,t,n,s)}};function Qr(e){return xt(e)?e:new Proxy(e,Po)}class Ao{constructor(t,n,s){this.fn=t,this.setter=n,this._value=void 0,this.dep=new ps(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=Ut-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!n,this.isSSR=s}notify(){if(this.flags|=16,!(this.flags&8)&&X!==this)return Fr(this,!0),!0}get value(){const t=this.dep.track();return Hr(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function To(e,t,n=!1){let s,r;return L(e)?s=e:(s=e.get,r=e.set),new Ao(s,r,n)}const nn={},hn=new WeakMap;let ft;function Oo(e,t=!1,n=ft){if(n){let s=hn.get(n);s||hn.set(n,s=[]),s.push(e)}}function Mo(e,t,n=Q){const{immediate:s,deep:r,once:i,scheduler:o,augmentJob:l,call:c}=n,d=O=>r?O:xe(O)||r===!1||r===0?ke(O,1):ke(O);let a,h,g,m,A=!1,T=!1;if(le(e)?(h=()=>e.value,A=xe(e)):xt(e)?(h=()=>d(e),A=!0):j(e)?(T=!0,A=e.some(O=>xt(O)||xe(O)),h=()=>e.map(O=>{if(le(O))return O.value;if(xt(O))return d(O);if(L(O))return c?c(O,2):O()})):L(e)?t?h=c?()=>c(e,2):e:h=()=>{if(g){qe();try{g()}finally{Ge()}}const O=ft;ft=a;try{return c?c(e,3,[m]):e(m)}finally{ft=O}}:h=De,t&&r){const O=h,z=r===!0?1/0:r;h=()=>ke(O(),z)}const V=io(),D=()=>{a.stop(),V&&V.active&&ls(V.effects,a)};if(i&&t){const O=t;t=(...z)=>{O(...z),D()}}let I=T?new Array(e.length).fill(nn):nn;const H=O=>{if(!(!(a.flags&1)||!a.dirty&&!O))if(t){const z=a.run();if(r||A||(T?z.some((se,ee)=>nt(se,I[ee])):nt(z,I))){g&&g();const se=ft;ft=a;try{const ee=[z,I===nn?void 0:T&&I[0]===nn?[]:I,m];I=z,c?c(t,3,ee):t(...ee)}finally{ft=se}}}else a.run()};return l&&l(H),a=new Mr(h),a.scheduler=o?()=>o(H,!1):H,m=O=>Oo(O,!1,a),g=a.onStop=()=>{const O=hn.get(a);if(O){if(c)c(O,4);else for(const z of O)z();hn.delete(a)}},t?s?H(!0):I=a.run():o?o(H.bind(null,!0),!0):a.run(),D.pause=a.pause.bind(a),D.resume=a.resume.bind(a),D.stop=D,D}function ke(e,t=1/0,n){if(t<=0||!Z(e)||e.__v_skip||(n=n||new Set,n.has(e)))return e;if(n.add(e),t--,le(e))ke(e.value,t,n);else if(j(e))for(let s=0;s<e.length;s++)ke(e[s],t,n);else if(Sn(e)||bt(e))e.forEach(s=>{ke(s,t,n)});else if(wr(e)){for(const s in e)ke(e[s],t,n);for(const s of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,s)&&ke(e[s],t,n)}return e}/**
* @vue/runtime-core v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function Yt(e,t,n,s){try{return s?e(...s):e()}catch(r){An(r,t,n)}}function je(e,t,n,s){if(L(e)){const r=Yt(e,t,n,s);return r&&Sr(r)&&r.catch(i=>{An(i,t,n)}),r}if(j(e)){const r=[];for(let i=0;i<e.length;i++)r.push(je(e[i],t,n,s));return r}}function An(e,t,n,s=!0){const r=t?t.vnode:null,{errorHandler:i,throwUnhandledErrorInProduction:o}=t&&t.appContext.config||Q;if(t){let l=t.parent;const c=t.proxy,d=`https://vuejs.org/error-reference/#runtime-${n}`;for(;l;){const a=l.ec;if(a){for(let h=0;h<a.length;h++)if(a[h](e,c,d)===!1)return}l=l.parent}if(i){qe(),Yt(i,null,10,[e,c,d]),Ge();return}}Io(e,n,r,s,o)}function Io(e,t,n,s=!0,r=!1){if(r)throw e;console.error(e)}const ue=[];let Fe=-1;const Et=[];let Ze=null,_t=0;const Yr=Promise.resolve();let dn=null;function ys(e){const t=dn||Yr;return e?t.then(this?e.bind(this):e):t}function Fo(e){let t=Fe+1,n=ue.length;for(;t<n;){const s=t+n>>>1,r=ue[s],i=Wt(r);i<e||i===e&&r.flags&2?t=s+1:n=s}return t}function vs(e){if(!(e.flags&1)){const t=Wt(e),n=ue[ue.length-1];!n||!(e.flags&2)&&t>=Wt(n)?ue.push(e):ue.splice(Fo(t),0,e),e.flags|=1,Xr()}}function Xr(){dn||(dn=Yr.then(ei))}function No(e){j(e)?Et.push(...e):Ze&&e.id===-1?Ze.splice(_t+1,0,e):e.flags&1||(Et.push(e),e.flags|=1),Xr()}function Is(e,t,n=Fe+1){for(;n<ue.length;n++){const s=ue[n];if(s&&s.flags&2){if(e&&s.id!==e.uid)continue;ue.splice(n,1),n--,s.flags&4&&(s.flags&=-2),s(),s.flags&4||(s.flags&=-2)}}}function Zr(e){if(Et.length){const t=[...new Set(Et)].sort((n,s)=>Wt(n)-Wt(s));if(Et.length=0,Ze){Ze.push(...t);return}for(Ze=t,_t=0;_t<Ze.length;_t++){const n=Ze[_t];n.flags&4&&(n.flags&=-2),n.flags&8||n(),n.flags&=-2}Ze=null,_t=0}}const Wt=e=>e.id==null?e.flags&2?-1:1/0:e.id;function ei(e){try{for(Fe=0;Fe<ue.length;Fe++){const t=ue[Fe];t&&!(t.flags&8)&&(t.flags&4&&(t.flags&=-2),Yt(t,t.i,t.i?15:14),t.flags&4||(t.flags&=-2))}}finally{for(;Fe<ue.length;Fe++){const t=ue[Fe];t&&(t.flags&=-2)}Fe=-1,ue.length=0,Zr(),dn=null,(ue.length||Et.length)&&ei()}}let ye=null,ti=null;function pn(e){const t=ye;return ye=e,ti=e&&e.type.__scopeId||null,t}function Do(e,t=ye,n){if(!t||e._n)return e;const s=(...r)=>{s._d&&Ks(-1);const i=pn(t);let o;try{o=e(...r)}finally{pn(i),s._d&&Ks(1)}return o};return s._n=!0,s._c=!0,s._d=!0,s}function mf(e,t){if(ye===null)return e;const n=In(ye),s=e.dirs||(e.dirs=[]);for(let r=0;r<t.length;r++){let[i,o,l,c=Q]=t[r];i&&(L(i)&&(i={mounted:i,updated:i}),i.deep&&ke(o),s.push({dir:i,instance:n,value:o,oldValue:void 0,arg:l,modifiers:c}))}return e}function lt(e,t,n,s){const r=e.dirs,i=t&&t.dirs;for(let o=0;o<r.length;o++){const l=r[o];i&&(l.oldValue=i[o].value);let c=l.dir[s];c&&(qe(),je(c,n,8,[e.el,l,e,t]),Ge())}}const Ho=Symbol("_vte"),jo=e=>e.__isTeleport;function bs(e,t){e.shapeFlag&6&&e.component?(e.transition=t,bs(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}/*! #__NO_SIDE_EFFECTS__ */function ni(e,t){return L(e)?ce({name:e.name},t,{setup:e}):e}function si(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function gn(e,t,n,s,r=!1){if(j(e)){e.forEach((A,T)=>gn(A,t&&(j(t)?t[T]:t),n,s,r));return}if($t(s)&&!r){s.shapeFlag&512&&s.type.__asyncResolved&&s.component.subTree.component&&gn(e,t,n,s.component.subTree);return}const i=s.shapeFlag&4?In(s.component):s.el,o=r?null:i,{i:l,r:c}=e,d=t&&t.r,a=l.refs===Q?l.refs={}:l.refs,h=l.setupState,g=k(h),m=h===Q?()=>!1:A=>W(g,A);if(d!=null&&d!==c&&(te(d)?(a[d]=null,m(d)&&(h[d]=null)):le(d)&&(d.value=null)),L(c))Yt(c,l,12,[o,a]);else{const A=te(c),T=le(c);if(A||T){const V=()=>{if(e.f){const D=A?m(c)?h[c]:a[c]:c.value;r?j(D)&&ls(D,i):j(D)?D.includes(i)||D.push(i):A?(a[c]=[i],m(c)&&(h[c]=a[c])):(c.value=[i],e.k&&(a[e.k]=c.value))}else A?(a[c]=o,m(c)&&(h[c]=o)):T&&(c.value=o,e.k&&(a[e.k]=o))};o?(V.id=-1,_e(V,n)):V()}}}wn().requestIdleCallback;wn().cancelIdleCallback;const $t=e=>!!e.type.__asyncLoader,ri=e=>e.type.__isKeepAlive;function $o(e,t){ii(e,"a",t)}function Lo(e,t){ii(e,"da",t)}function ii(e,t,n=oe){const s=e.__wdc||(e.__wdc=()=>{let r=n;for(;r;){if(r.isDeactivated)return;r=r.parent}return e()});if(Tn(t,s,n),n){let r=n.parent;for(;r&&r.parent;)ri(r.parent.vnode)&&Vo(s,t,n,r),r=r.parent}}function Vo(e,t,n,s){const r=Tn(t,e,s,!0);oi(()=>{ls(s[t],r)},n)}function Tn(e,t,n=oe,s=!1){if(n){const r=n[e]||(n[e]=[]),i=t.__weh||(t.__weh=(...o)=>{qe();const l=Xt(n),c=je(t,n,e,o);return l(),Ge(),c});return s?r.unshift(i):r.push(i),i}}const ze=e=>(t,n=oe)=>{(!Gt||e==="sp")&&Tn(e,(...s)=>t(...s),n)},Ko=ze("bm"),Bo=ze("m"),Uo=ze("bu"),ko=ze("u"),Wo=ze("bum"),oi=ze("um"),qo=ze("sp"),Go=ze("rtg"),zo=ze("rtc");function Jo(e,t=oe){Tn("ec",e,t)}const Qo=Symbol.for("v-ndc");function _f(e,t,n,s){let r;const i=n,o=j(e);if(o||te(e)){const l=o&&xt(e);let c=!1,d=!1;l&&(c=!xe(e),d=rt(e),e=Cn(e)),r=new Array(e.length);for(let a=0,h=e.length;a<h;a++)r[a]=t(c?d?an(re(e[a])):re(e[a]):e[a],a,void 0,i)}else if(typeof e=="number"){r=new Array(e);for(let l=0;l<e;l++)r[l]=t(l+1,l,void 0,i)}else if(Z(e))if(e[Symbol.iterator])r=Array.from(e,(l,c)=>t(l,c,void 0,i));else{const l=Object.keys(e);r=new Array(l.length);for(let c=0,d=l.length;c<d;c++){const a=l[c];r[c]=t(e[a],a,c,i)}}else r=[];return r}const Qn=e=>e?Pi(e)?In(e):Qn(e.parent):null,Lt=ce(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>Qn(e.parent),$root:e=>Qn(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>ci(e),$forceUpdate:e=>e.f||(e.f=()=>{vs(e.update)}),$nextTick:e=>e.n||(e.n=ys.bind(e.proxy)),$watch:e=>_l.bind(e)}),Ln=(e,t)=>e!==Q&&!e.__isScriptSetup&&W(e,t),Yo={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:n,setupState:s,data:r,props:i,accessCache:o,type:l,appContext:c}=e;let d;if(t[0]!=="$"){const m=o[t];if(m!==void 0)switch(m){case 1:return s[t];case 2:return r[t];case 4:return n[t];case 3:return i[t]}else{if(Ln(s,t))return o[t]=1,s[t];if(r!==Q&&W(r,t))return o[t]=2,r[t];if((d=e.propsOptions[0])&&W(d,t))return o[t]=3,i[t];if(n!==Q&&W(n,t))return o[t]=4,n[t];Yn&&(o[t]=0)}}const a=Lt[t];let h,g;if(a)return t==="$attrs"&&ie(e.attrs,"get",""),a(e);if((h=l.__cssModules)&&(h=h[t]))return h;if(n!==Q&&W(n,t))return o[t]=4,n[t];if(g=c.config.globalProperties,W(g,t))return g[t]},set({_:e},t,n){const{data:s,setupState:r,ctx:i}=e;return Ln(r,t)?(r[t]=n,!0):s!==Q&&W(s,t)?(s[t]=n,!0):W(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(i[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:s,appContext:r,propsOptions:i}},o){let l;return!!n[o]||e!==Q&&W(e,o)||Ln(t,o)||(l=i[0])&&W(l,o)||W(s,o)||W(Lt,o)||W(r.config.globalProperties,o)},defineProperty(e,t,n){return n.get!=null?e._.accessCache[t]=0:W(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function Fs(e){return j(e)?e.reduce((t,n)=>(t[n]=null,t),{}):e}let Yn=!0;function Xo(e){const t=ci(e),n=e.proxy,s=e.ctx;Yn=!1,t.beforeCreate&&Ns(t.beforeCreate,e,"bc");const{data:r,computed:i,methods:o,watch:l,provide:c,inject:d,created:a,beforeMount:h,mounted:g,beforeUpdate:m,updated:A,activated:T,deactivated:V,beforeDestroy:D,beforeUnmount:I,destroyed:H,unmounted:O,render:z,renderTracked:se,renderTriggered:ee,errorCaptured:Re,serverPrefetch:Je,expose:Ce,inheritAttrs:Qe,components:ot,directives:Pe,filters:Tt}=t;if(d&&Zo(d,s,null),o)for(const G in o){const B=o[G];L(B)&&(s[G]=B.bind(n))}if(r){const G=r.call(n,n);Z(G)&&(e.data=Pn(G))}if(Yn=!0,i)for(const G in i){const B=i[G],$e=L(B)?B.bind(n,n):L(B.get)?B.get.bind(n,n):De,Ye=!L(B)&&L(B.set)?B.set.bind(n):De,Ae=Se({get:$e,set:Ye});Object.defineProperty(s,G,{enumerable:!0,configurable:!0,get:()=>Ae.value,set:ae=>Ae.value=ae})}if(l)for(const G in l)li(l[G],s,n,G);if(c){const G=L(c)?c.call(n):c;Reflect.ownKeys(G).forEach(B=>{rn(B,G[B])})}a&&Ns(a,e,"c");function ne(G,B){j(B)?B.forEach($e=>G($e.bind(n))):B&&G(B.bind(n))}if(ne(Ko,h),ne(Bo,g),ne(Uo,m),ne(ko,A),ne($o,T),ne(Lo,V),ne(Jo,Re),ne(zo,se),ne(Go,ee),ne(Wo,I),ne(oi,O),ne(qo,Je),j(Ce))if(Ce.length){const G=e.exposed||(e.exposed={});Ce.forEach(B=>{Object.defineProperty(G,B,{get:()=>n[B],set:$e=>n[B]=$e})})}else e.exposed||(e.exposed={});z&&e.render===De&&(e.render=z),Qe!=null&&(e.inheritAttrs=Qe),ot&&(e.components=ot),Pe&&(e.directives=Pe),Je&&si(e)}function Zo(e,t,n=De){j(e)&&(e=Xn(e));for(const s in e){const r=e[s];let i;Z(r)?"default"in r?i=We(r.from||s,r.default,!0):i=We(r.from||s):i=We(r),le(i)?Object.defineProperty(t,s,{enumerable:!0,configurable:!0,get:()=>i.value,set:o=>i.value=o}):t[s]=i}}function Ns(e,t,n){je(j(e)?e.map(s=>s.bind(t.proxy)):e.bind(t.proxy),t,n)}function li(e,t,n,s){let r=s.includes(".")?xi(n,s):()=>n[s];if(te(e)){const i=t[e];L(i)&&on(r,i)}else if(L(e))on(r,e.bind(n));else if(Z(e))if(j(e))e.forEach(i=>li(i,t,n,s));else{const i=L(e.handler)?e.handler.bind(n):t[e.handler];L(i)&&on(r,i,e)}}function ci(e){const t=e.type,{mixins:n,extends:s}=t,{mixins:r,optionsCache:i,config:{optionMergeStrategies:o}}=e.appContext,l=i.get(t);let c;return l?c=l:!r.length&&!n&&!s?c=t:(c={},r.length&&r.forEach(d=>mn(c,d,o,!0)),mn(c,t,o)),Z(t)&&i.set(t,c),c}function mn(e,t,n,s=!1){const{mixins:r,extends:i}=t;i&&mn(e,i,n,!0),r&&r.forEach(o=>mn(e,o,n,!0));for(const o in t)if(!(s&&o==="expose")){const l=el[o]||n&&n[o];e[o]=l?l(e[o],t[o]):t[o]}return e}const el={data:Ds,props:Hs,emits:Hs,methods:Nt,computed:Nt,beforeCreate:fe,created:fe,beforeMount:fe,mounted:fe,beforeUpdate:fe,updated:fe,beforeDestroy:fe,beforeUnmount:fe,destroyed:fe,unmounted:fe,activated:fe,deactivated:fe,errorCaptured:fe,serverPrefetch:fe,components:Nt,directives:Nt,watch:nl,provide:Ds,inject:tl};function Ds(e,t){return t?e?function(){return ce(L(e)?e.call(this,this):e,L(t)?t.call(this,this):t)}:t:e}function tl(e,t){return Nt(Xn(e),Xn(t))}function Xn(e){if(j(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function fe(e,t){return e?[...new Set([].concat(e,t))]:t}function Nt(e,t){return e?ce(Object.create(null),e,t):t}function Hs(e,t){return e?j(e)&&j(t)?[...new Set([...e,...t])]:ce(Object.create(null),Fs(e),Fs(t??{})):t}function nl(e,t){if(!e)return t;if(!t)return e;const n=ce(Object.create(null),e);for(const s in t)n[s]=fe(e[s],t[s]);return n}function fi(){return{app:null,config:{isNativeTag:ki,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let sl=0;function rl(e,t){return function(s,r=null){L(s)||(s=ce({},s)),r!=null&&!Z(r)&&(r=null);const i=fi(),o=new WeakSet,l=[];let c=!1;const d=i.app={_uid:sl++,_component:s,_props:r,_container:null,_context:i,_instance:null,version:Vl,get config(){return i.config},set config(a){},use(a,...h){return o.has(a)||(a&&L(a.install)?(o.add(a),a.install(d,...h)):L(a)&&(o.add(a),a(d,...h))),d},mixin(a){return i.mixins.includes(a)||i.mixins.push(a),d},component(a,h){return h?(i.components[a]=h,d):i.components[a]},directive(a,h){return h?(i.directives[a]=h,d):i.directives[a]},mount(a,h,g){if(!c){const m=d._ceVNode||pe(s,r);return m.appContext=i,g===!0?g="svg":g===!1&&(g=void 0),e(m,a,g),c=!0,d._container=a,a.__vue_app__=d,In(m.component)}},onUnmount(a){l.push(a)},unmount(){c&&(je(l,d._instance,16),e(null,d._container),delete d._container.__vue_app__)},provide(a,h){return i.provides[a]=h,d},runWithContext(a){const h=wt;wt=d;try{return a()}finally{wt=h}}};return d}}let wt=null;function rn(e,t){if(oe){let n=oe.provides;const s=oe.parent&&oe.parent.provides;s===n&&(n=oe.provides=Object.create(s)),n[e]=t}}function We(e,t,n=!1){const s=oe||ye;if(s||wt){let r=wt?wt._context.provides:s?s.parent==null||s.ce?s.vnode.appContext&&s.vnode.appContext.provides:s.parent.provides:void 0;if(r&&e in r)return r[e];if(arguments.length>1)return n&&L(t)?t.call(s&&s.proxy):t}}const ui={},ai=()=>Object.create(ui),hi=e=>Object.getPrototypeOf(e)===ui;function il(e,t,n,s=!1){const r={},i=ai();e.propsDefaults=Object.create(null),di(e,t,r,i);for(const o in e.propsOptions[0])o in r||(r[o]=void 0);n?e.props=s?r:Wr(r):e.type.props?e.props=r:e.props=i,e.attrs=i}function ol(e,t,n,s){const{props:r,attrs:i,vnode:{patchFlag:o}}=e,l=k(r),[c]=e.propsOptions;let d=!1;if((s||o>0)&&!(o&16)){if(o&8){const a=e.vnode.dynamicProps;for(let h=0;h<a.length;h++){let g=a[h];if(On(e.emitsOptions,g))continue;const m=t[g];if(c)if(W(i,g))m!==i[g]&&(i[g]=m,d=!0);else{const A=st(g);r[A]=Zn(c,l,A,m,e,!1)}else m!==i[g]&&(i[g]=m,d=!0)}}}else{di(e,t,r,i)&&(d=!0);let a;for(const h in l)(!t||!W(t,h)&&((a=ht(h))===h||!W(t,a)))&&(c?n&&(n[h]!==void 0||n[a]!==void 0)&&(r[h]=Zn(c,l,h,void 0,e,!0)):delete r[h]);if(i!==l)for(const h in i)(!t||!W(t,h))&&(delete i[h],d=!0)}d&&Ue(e.attrs,"set","")}function di(e,t,n,s){const[r,i]=e.propsOptions;let o=!1,l;if(t)for(let c in t){if(Dt(c))continue;const d=t[c];let a;r&&W(r,a=st(c))?!i||!i.includes(a)?n[a]=d:(l||(l={}))[a]=d:On(e.emitsOptions,c)||(!(c in s)||d!==s[c])&&(s[c]=d,o=!0)}if(i){const c=k(n),d=l||Q;for(let a=0;a<i.length;a++){const h=i[a];n[h]=Zn(r,c,h,d[h],e,!W(d,h))}}return o}function Zn(e,t,n,s,r,i){const o=e[n];if(o!=null){const l=W(o,"default");if(l&&s===void 0){const c=o.default;if(o.type!==Function&&!o.skipFactory&&L(c)){const{propsDefaults:d}=r;if(n in d)s=d[n];else{const a=Xt(r);s=d[n]=c.call(null,t),a()}}else s=c;r.ce&&r.ce._setProp(n,s)}o[0]&&(i&&!l?s=!1:o[1]&&(s===""||s===ht(n))&&(s=!0))}return s}const ll=new WeakMap;function pi(e,t,n=!1){const s=n?ll:t.propsCache,r=s.get(e);if(r)return r;const i=e.props,o={},l=[];let c=!1;if(!L(e)){const a=h=>{c=!0;const[g,m]=pi(h,t,!0);ce(o,g),m&&l.push(...m)};!n&&t.mixins.length&&t.mixins.forEach(a),e.extends&&a(e.extends),e.mixins&&e.mixins.forEach(a)}if(!i&&!c)return Z(e)&&s.set(e,vt),vt;if(j(i))for(let a=0;a<i.length;a++){const h=st(i[a]);js(h)&&(o[h]=Q)}else if(i)for(const a in i){const h=st(a);if(js(h)){const g=i[a],m=o[h]=j(g)||L(g)?{type:g}:ce({},g),A=m.type;let T=!1,V=!0;if(j(A))for(let D=0;D<A.length;++D){const I=A[D],H=L(I)&&I.name;if(H==="Boolean"){T=!0;break}else H==="String"&&(V=!1)}else T=L(A)&&A.name==="Boolean";m[0]=T,m[1]=V,(T||W(m,"default"))&&l.push(h)}}const d=[o,l];return Z(e)&&s.set(e,d),d}function js(e){return e[0]!=="$"&&!Dt(e)}const xs=e=>e[0]==="_"||e==="$stable",Ss=e=>j(e)?e.map(Ne):[Ne(e)],cl=(e,t,n)=>{if(t._n)return t;const s=Do((...r)=>Ss(t(...r)),n);return s._c=!1,s},gi=(e,t,n)=>{const s=e._ctx;for(const r in e){if(xs(r))continue;const i=e[r];if(L(i))t[r]=cl(r,i,s);else if(i!=null){const o=Ss(i);t[r]=()=>o}}},mi=(e,t)=>{const n=Ss(t);e.slots.default=()=>n},_i=(e,t,n)=>{for(const s in t)(n||!xs(s))&&(e[s]=t[s])},fl=(e,t,n)=>{const s=e.slots=ai();if(e.vnode.shapeFlag&32){const r=t._;r?(_i(s,t,n),n&&Cr(s,"_",r,!0)):gi(t,s)}else t&&mi(e,t)},ul=(e,t,n)=>{const{vnode:s,slots:r}=e;let i=!0,o=Q;if(s.shapeFlag&32){const l=t._;l?n&&l===1?i=!1:_i(r,t,n):(i=!t.$stable,gi(t,r)),o=t}else t&&(mi(e,t),o={default:1});if(i)for(const l in r)!xs(l)&&o[l]==null&&delete r[l]},_e=wl;function al(e){return hl(e)}function hl(e,t){const n=wn();n.__VUE__=!0;const{insert:s,remove:r,patchProp:i,createElement:o,createText:l,createComment:c,setText:d,setElementText:a,parentNode:h,nextSibling:g,setScopeId:m=De,insertStaticContent:A}=e,T=(f,u,p,_=null,b=null,v=null,w=void 0,E=null,S=!!u.dynamicChildren)=>{if(f===u)return;f&&!It(f,u)&&(_=y(f),ae(f,b,v,!0),f=null),u.patchFlag===-2&&(S=!1,u.dynamicChildren=null);const{type:x,ref:N,shapeFlag:C}=u;switch(x){case Mn:V(f,u,p,_);break;case it:D(f,u,p,_);break;case ln:f==null&&I(u,p,_,w);break;case Be:ot(f,u,p,_,b,v,w,E,S);break;default:C&1?z(f,u,p,_,b,v,w,E,S):C&6?Pe(f,u,p,_,b,v,w,E,S):(C&64||C&128)&&x.process(f,u,p,_,b,v,w,E,S,M)}N!=null&&b&&gn(N,f&&f.ref,v,u||f,!u)},V=(f,u,p,_)=>{if(f==null)s(u.el=l(u.children),p,_);else{const b=u.el=f.el;u.children!==f.children&&d(b,u.children)}},D=(f,u,p,_)=>{f==null?s(u.el=c(u.children||""),p,_):u.el=f.el},I=(f,u,p,_)=>{[f.el,f.anchor]=A(f.children,u,p,_,f.el,f.anchor)},H=({el:f,anchor:u},p,_)=>{let b;for(;f&&f!==u;)b=g(f),s(f,p,_),f=b;s(u,p,_)},O=({el:f,anchor:u})=>{let p;for(;f&&f!==u;)p=g(f),r(f),f=p;r(u)},z=(f,u,p,_,b,v,w,E,S)=>{u.type==="svg"?w="svg":u.type==="math"&&(w="mathml"),f==null?se(u,p,_,b,v,w,E,S):Je(f,u,b,v,w,E,S)},se=(f,u,p,_,b,v,w,E)=>{let S,x;const{props:N,shapeFlag:C,transition:F,dirs:$}=f;if(S=f.el=o(f.type,v,N&&N.is,N),C&8?a(S,f.children):C&16&&Re(f.children,S,null,_,b,Vn(f,v),w,E),$&&lt(f,null,_,"created"),ee(S,f,f.scopeId,w,_),N){for(const Y in N)Y!=="value"&&!Dt(Y)&&i(S,Y,null,N[Y],v,_);"value"in N&&i(S,"value",null,N.value,v),(x=N.onVnodeBeforeMount)&&Ie(x,_,f)}$&&lt(f,null,_,"beforeMount");const K=dl(b,F);K&&F.beforeEnter(S),s(S,u,p),((x=N&&N.onVnodeMounted)||K||$)&&_e(()=>{x&&Ie(x,_,f),K&&F.enter(S),$&&lt(f,null,_,"mounted")},b)},ee=(f,u,p,_,b)=>{if(p&&m(f,p),_)for(let v=0;v<_.length;v++)m(f,_[v]);if(b){let v=b.subTree;if(u===v||Ei(v.type)&&(v.ssContent===u||v.ssFallback===u)){const w=b.vnode;ee(f,w,w.scopeId,w.slotScopeIds,b.parent)}}},Re=(f,u,p,_,b,v,w,E,S=0)=>{for(let x=S;x<f.length;x++){const N=f[x]=E?et(f[x]):Ne(f[x]);T(null,N,u,p,_,b,v,w,E)}},Je=(f,u,p,_,b,v,w)=>{const E=u.el=f.el;let{patchFlag:S,dynamicChildren:x,dirs:N}=u;S|=f.patchFlag&16;const C=f.props||Q,F=u.props||Q;let $;if(p&&ct(p,!1),($=F.onVnodeBeforeUpdate)&&Ie($,p,u,f),N&&lt(u,f,p,"beforeUpdate"),p&&ct(p,!0),(C.innerHTML&&F.innerHTML==null||C.textContent&&F.textContent==null)&&a(E,""),x?Ce(f.dynamicChildren,x,E,p,_,Vn(u,b),v):w||B(f,u,E,null,p,_,Vn(u,b),v,!1),S>0){if(S&16)Qe(E,C,F,p,b);else if(S&2&&C.class!==F.class&&i(E,"class",null,F.class,b),S&4&&i(E,"style",C.style,F.style,b),S&8){const K=u.dynamicProps;for(let Y=0;Y<K.length;Y++){const q=K[Y],ge=C[q],he=F[q];(he!==ge||q==="value")&&i(E,q,ge,he,b,p)}}S&1&&f.children!==u.children&&a(E,u.children)}else!w&&x==null&&Qe(E,C,F,p,b);(($=F.onVnodeUpdated)||N)&&_e(()=>{$&&Ie($,p,u,f),N&&lt(u,f,p,"updated")},_)},Ce=(f,u,p,_,b,v,w)=>{for(let E=0;E<u.length;E++){const S=f[E],x=u[E],N=S.el&&(S.type===Be||!It(S,x)||S.shapeFlag&198)?h(S.el):p;T(S,x,N,null,_,b,v,w,!0)}},Qe=(f,u,p,_,b)=>{if(u!==p){if(u!==Q)for(const v in u)!Dt(v)&&!(v in p)&&i(f,v,u[v],null,b,_);for(const v in p){if(Dt(v))continue;const w=p[v],E=u[v];w!==E&&v!=="value"&&i(f,v,E,w,b,_)}"value"in p&&i(f,"value",u.value,p.value,b)}},ot=(f,u,p,_,b,v,w,E,S)=>{const x=u.el=f?f.el:l(""),N=u.anchor=f?f.anchor:l("");let{patchFlag:C,dynamicChildren:F,slotScopeIds:$}=u;$&&(E=E?E.concat($):$),f==null?(s(x,p,_),s(N,p,_),Re(u.children||[],p,N,b,v,w,E,S)):C>0&&C&64&&F&&f.dynamicChildren?(Ce(f.dynamicChildren,F,p,b,v,w,E),(u.key!=null||b&&u===b.subTree)&&yi(f,u,!0)):B(f,u,p,N,b,v,w,E,S)},Pe=(f,u,p,_,b,v,w,E,S)=>{u.slotScopeIds=E,f==null?u.shapeFlag&512?b.ctx.activate(u,p,_,w,S):Tt(u,p,_,b,v,w,S):dt(f,u,S)},Tt=(f,u,p,_,b,v,w)=>{const E=f.component=Nl(f,_,b);if(ri(f)&&(E.ctx.renderer=M),Dl(E,!1,w),E.asyncDep){if(b&&b.registerDep(E,ne,w),!f.el){const S=E.subTree=pe(it);D(null,S,u,p)}}else ne(E,f,u,p,b,v,w)},dt=(f,u,p)=>{const _=u.component=f.component;if(Sl(f,u,p))if(_.asyncDep&&!_.asyncResolved){G(_,u,p);return}else _.next=u,_.update();else u.el=f.el,_.vnode=u},ne=(f,u,p,_,b,v,w)=>{const E=()=>{if(f.isMounted){let{next:C,bu:F,u:$,parent:K,vnode:Y}=f;{const Oe=vi(f);if(Oe){C&&(C.el=Y.el,G(f,C,w)),Oe.asyncDep.then(()=>{f.isUnmounted||E()});return}}let q=C,ge;ct(f,!1),C?(C.el=Y.el,G(f,C,w)):C=Y,F&&sn(F),(ge=C.props&&C.props.onVnodeBeforeUpdate)&&Ie(ge,K,C,Y),ct(f,!0);const he=Ls(f),Te=f.subTree;f.subTree=he,T(Te,he,h(Te.el),y(Te),f,b,v),C.el=he.el,q===null&&El(f,he.el),$&&_e($,b),(ge=C.props&&C.props.onVnodeUpdated)&&_e(()=>Ie(ge,K,C,Y),b)}else{let C;const{el:F,props:$}=u,{bm:K,m:Y,parent:q,root:ge,type:he}=f,Te=$t(u);ct(f,!1),K&&sn(K),!Te&&(C=$&&$.onVnodeBeforeMount)&&Ie(C,q,u),ct(f,!0);{ge.ce&&ge.ce._injectChildStyle(he);const Oe=f.subTree=Ls(f);T(null,Oe,p,_,f,b,v),u.el=Oe.el}if(Y&&_e(Y,b),!Te&&(C=$&&$.onVnodeMounted)){const Oe=u;_e(()=>Ie(C,q,Oe),b)}(u.shapeFlag&256||q&&$t(q.vnode)&&q.vnode.shapeFlag&256)&&f.a&&_e(f.a,b),f.isMounted=!0,u=p=_=null}};f.scope.on();const S=f.effect=new Mr(E);f.scope.off();const x=f.update=S.run.bind(S),N=f.job=S.runIfDirty.bind(S);N.i=f,N.id=f.uid,S.scheduler=()=>vs(N),ct(f,!0),x()},G=(f,u,p)=>{u.component=f;const _=f.vnode.props;f.vnode=u,f.next=null,ol(f,u.props,_,p),ul(f,u.children,p),qe(),Is(f),Ge()},B=(f,u,p,_,b,v,w,E,S=!1)=>{const x=f&&f.children,N=f?f.shapeFlag:0,C=u.children,{patchFlag:F,shapeFlag:$}=u;if(F>0){if(F&128){Ye(x,C,p,_,b,v,w,E,S);return}else if(F&256){$e(x,C,p,_,b,v,w,E,S);return}}$&8?(N&16&&be(x,b,v),C!==x&&a(p,C)):N&16?$&16?Ye(x,C,p,_,b,v,w,E,S):be(x,b,v,!0):(N&8&&a(p,""),$&16&&Re(C,p,_,b,v,w,E,S))},$e=(f,u,p,_,b,v,w,E,S)=>{f=f||vt,u=u||vt;const x=f.length,N=u.length,C=Math.min(x,N);let F;for(F=0;F<C;F++){const $=u[F]=S?et(u[F]):Ne(u[F]);T(f[F],$,p,null,b,v,w,E,S)}x>N?be(f,b,v,!0,!1,C):Re(u,p,_,b,v,w,E,S,C)},Ye=(f,u,p,_,b,v,w,E,S)=>{let x=0;const N=u.length;let C=f.length-1,F=N-1;for(;x<=C&&x<=F;){const $=f[x],K=u[x]=S?et(u[x]):Ne(u[x]);if(It($,K))T($,K,p,null,b,v,w,E,S);else break;x++}for(;x<=C&&x<=F;){const $=f[C],K=u[F]=S?et(u[F]):Ne(u[F]);if(It($,K))T($,K,p,null,b,v,w,E,S);else break;C--,F--}if(x>C){if(x<=F){const $=F+1,K=$<N?u[$].el:_;for(;x<=F;)T(null,u[x]=S?et(u[x]):Ne(u[x]),p,K,b,v,w,E,S),x++}}else if(x>F)for(;x<=C;)ae(f[x],b,v,!0),x++;else{const $=x,K=x,Y=new Map;for(x=K;x<=F;x++){const me=u[x]=S?et(u[x]):Ne(u[x]);me.key!=null&&Y.set(me.key,x)}let q,ge=0;const he=F-K+1;let Te=!1,Oe=0;const Ot=new Array(he);for(x=0;x<he;x++)Ot[x]=0;for(x=$;x<=C;x++){const me=f[x];if(ge>=he){ae(me,b,v,!0);continue}let Me;if(me.key!=null)Me=Y.get(me.key);else for(q=K;q<=F;q++)if(Ot[q-K]===0&&It(me,u[q])){Me=q;break}Me===void 0?ae(me,b,v,!0):(Ot[Me-K]=x+1,Me>=Oe?Oe=Me:Te=!0,T(me,u[Me],p,null,b,v,w,E,S),ge++)}const Cs=Te?pl(Ot):vt;for(q=Cs.length-1,x=he-1;x>=0;x--){const me=K+x,Me=u[me],Ps=me+1<N?u[me+1].el:_;Ot[x]===0?T(null,Me,p,Ps,b,v,w,E,S):Te&&(q<0||x!==Cs[q]?Ae(Me,p,Ps,2):q--)}}},Ae=(f,u,p,_,b=null)=>{const{el:v,type:w,transition:E,children:S,shapeFlag:x}=f;if(x&6){Ae(f.component.subTree,u,p,_);return}if(x&128){f.suspense.move(u,p,_);return}if(x&64){w.move(f,u,p,M);return}if(w===Be){s(v,u,p);for(let C=0;C<S.length;C++)Ae(S[C],u,p,_);s(f.anchor,u,p);return}if(w===ln){H(f,u,p);return}if(_!==2&&x&1&&E)if(_===0)E.beforeEnter(v),s(v,u,p),_e(()=>E.enter(v),b);else{const{leave:C,delayLeave:F,afterLeave:$}=E,K=()=>{f.ctx.isUnmounted?r(v):s(v,u,p)},Y=()=>{C(v,()=>{K(),$&&$()})};F?F(v,K,Y):Y()}else s(v,u,p)},ae=(f,u,p,_=!1,b=!1)=>{const{type:v,props:w,ref:E,children:S,dynamicChildren:x,shapeFlag:N,patchFlag:C,dirs:F,cacheIndex:$}=f;if(C===-2&&(b=!1),E!=null&&(qe(),gn(E,null,p,f,!0),Ge()),$!=null&&(u.renderCache[$]=void 0),N&256){u.ctx.deactivate(f);return}const K=N&1&&F,Y=!$t(f);let q;if(Y&&(q=w&&w.onVnodeBeforeUnmount)&&Ie(q,u,f),N&6)Zt(f.component,p,_);else{if(N&128){f.suspense.unmount(p,_);return}K&&lt(f,null,u,"beforeUnmount"),N&64?f.type.remove(f,u,p,M,_):x&&!x.hasOnce&&(v!==Be||C>0&&C&64)?be(x,u,p,!1,!0):(v===Be&&C&384||!b&&N&16)&&be(S,u,p),_&&pt(f)}(Y&&(q=w&&w.onVnodeUnmounted)||K)&&_e(()=>{q&&Ie(q,u,f),K&&lt(f,null,u,"unmounted")},p)},pt=f=>{const{type:u,el:p,anchor:_,transition:b}=f;if(u===Be){gt(p,_);return}if(u===ln){O(f);return}const v=()=>{r(p),b&&!b.persisted&&b.afterLeave&&b.afterLeave()};if(f.shapeFlag&1&&b&&!b.persisted){const{leave:w,delayLeave:E}=b,S=()=>w(p,v);E?E(f.el,v,S):S()}else v()},gt=(f,u)=>{let p;for(;f!==u;)p=g(f),r(f),f=p;r(u)},Zt=(f,u,p)=>{const{bum:_,scope:b,job:v,subTree:w,um:E,m:S,a:x,parent:N,slots:{__:C}}=f;$s(S),$s(x),_&&sn(_),N&&j(C)&&C.forEach(F=>{N.renderCache[F]=void 0}),b.stop(),v&&(v.flags|=8,ae(w,f,u,p)),E&&_e(E,u),_e(()=>{f.isUnmounted=!0},u),u&&u.pendingBranch&&!u.isUnmounted&&f.asyncDep&&!f.asyncResolved&&f.suspenseId===u.pendingId&&(u.deps--,u.deps===0&&u.resolve())},be=(f,u,p,_=!1,b=!1,v=0)=>{for(let w=v;w<f.length;w++)ae(f[w],u,p,_,b)},y=f=>{if(f.shapeFlag&6)return y(f.component.subTree);if(f.shapeFlag&128)return f.suspense.next();const u=g(f.anchor||f.el),p=u&&u[Ho];return p?g(p):u};let P=!1;const R=(f,u,p)=>{f==null?u._vnode&&ae(u._vnode,null,null,!0):T(u._vnode||null,f,u,null,null,null,p),u._vnode=f,P||(P=!0,Is(),Zr(),P=!1)},M={p:T,um:ae,m:Ae,r:pt,mt:Tt,mc:Re,pc:B,pbc:Ce,n:y,o:e};return{render:R,hydrate:void 0,createApp:rl(R)}}function Vn({type:e,props:t},n){return n==="svg"&&e==="foreignObject"||n==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function ct({effect:e,job:t},n){n?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function dl(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function yi(e,t,n=!1){const s=e.children,r=t.children;if(j(s)&&j(r))for(let i=0;i<s.length;i++){const o=s[i];let l=r[i];l.shapeFlag&1&&!l.dynamicChildren&&((l.patchFlag<=0||l.patchFlag===32)&&(l=r[i]=et(r[i]),l.el=o.el),!n&&l.patchFlag!==-2&&yi(o,l)),l.type===Mn&&(l.el=o.el),l.type===it&&!l.el&&(l.el=o.el)}}function pl(e){const t=e.slice(),n=[0];let s,r,i,o,l;const c=e.length;for(s=0;s<c;s++){const d=e[s];if(d!==0){if(r=n[n.length-1],e[r]<d){t[s]=r,n.push(s);continue}for(i=0,o=n.length-1;i<o;)l=i+o>>1,e[n[l]]<d?i=l+1:o=l;d<e[n[i]]&&(i>0&&(t[s]=n[i-1]),n[i]=s)}}for(i=n.length,o=n[i-1];i-- >0;)n[i]=o,o=t[o];return n}function vi(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:vi(t)}function $s(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const gl=Symbol.for("v-scx"),ml=()=>We(gl);function on(e,t,n){return bi(e,t,n)}function bi(e,t,n=Q){const{immediate:s,deep:r,flush:i,once:o}=n,l=ce({},n),c=t&&s||!t&&i!=="post";let d;if(Gt){if(i==="sync"){const m=ml();d=m.__watcherHandles||(m.__watcherHandles=[])}else if(!c){const m=()=>{};return m.stop=De,m.resume=De,m.pause=De,m}}const a=oe;l.call=(m,A,T)=>je(m,a,A,T);let h=!1;i==="post"?l.scheduler=m=>{_e(m,a&&a.suspense)}:i!=="sync"&&(h=!0,l.scheduler=(m,A)=>{A?m():vs(m)}),l.augmentJob=m=>{t&&(m.flags|=4),h&&(m.flags|=2,a&&(m.id=a.uid,m.i=a))};const g=Mo(e,t,l);return Gt&&(d?d.push(g):c&&g()),g}function _l(e,t,n){const s=this.proxy,r=te(e)?e.includes(".")?xi(s,e):()=>s[e]:e.bind(s,s);let i;L(t)?i=t:(i=t.handler,n=t);const o=Xt(this),l=bi(r,i.bind(s),n);return o(),l}function xi(e,t){const n=t.split(".");return()=>{let s=e;for(let r=0;r<n.length&&s;r++)s=s[n[r]];return s}}const yl=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${st(t)}Modifiers`]||e[`${ht(t)}Modifiers`];function vl(e,t,...n){if(e.isUnmounted)return;const s=e.vnode.props||Q;let r=n;const i=t.startsWith("update:"),o=i&&yl(s,t.slice(7));o&&(o.trim&&(r=n.map(a=>te(a)?a.trim():a)),o.number&&(r=n.map(un)));let l,c=s[l=Nn(t)]||s[l=Nn(st(t))];!c&&i&&(c=s[l=Nn(ht(t))]),c&&je(c,e,6,r);const d=s[l+"Once"];if(d){if(!e.emitted)e.emitted={};else if(e.emitted[l])return;e.emitted[l]=!0,je(d,e,6,r)}}function Si(e,t,n=!1){const s=t.emitsCache,r=s.get(e);if(r!==void 0)return r;const i=e.emits;let o={},l=!1;if(!L(e)){const c=d=>{const a=Si(d,t,!0);a&&(l=!0,ce(o,a))};!n&&t.mixins.length&&t.mixins.forEach(c),e.extends&&c(e.extends),e.mixins&&e.mixins.forEach(c)}return!i&&!l?(Z(e)&&s.set(e,null),null):(j(i)?i.forEach(c=>o[c]=null):ce(o,i),Z(e)&&s.set(e,o),o)}function On(e,t){return!e||!xn(t)?!1:(t=t.slice(2).replace(/Once$/,""),W(e,t[0].toLowerCase()+t.slice(1))||W(e,ht(t))||W(e,t))}function Ls(e){const{type:t,vnode:n,proxy:s,withProxy:r,propsOptions:[i],slots:o,attrs:l,emit:c,render:d,renderCache:a,props:h,data:g,setupState:m,ctx:A,inheritAttrs:T}=e,V=pn(e);let D,I;try{if(n.shapeFlag&4){const O=r||s,z=O;D=Ne(d.call(z,O,a,h,m,g,A)),I=l}else{const O=t;D=Ne(O.length>1?O(h,{attrs:l,slots:o,emit:c}):O(h,null)),I=t.props?l:bl(l)}}catch(O){Vt.length=0,An(O,e,1),D=pe(it)}let H=D;if(I&&T!==!1){const O=Object.keys(I),{shapeFlag:z}=H;O.length&&z&7&&(i&&O.some(os)&&(I=xl(I,i)),H=Ct(H,I,!1,!0))}return n.dirs&&(H=Ct(H,null,!1,!0),H.dirs=H.dirs?H.dirs.concat(n.dirs):n.dirs),n.transition&&bs(H,n.transition),D=H,pn(V),D}const bl=e=>{let t;for(const n in e)(n==="class"||n==="style"||xn(n))&&((t||(t={}))[n]=e[n]);return t},xl=(e,t)=>{const n={};for(const s in e)(!os(s)||!(s.slice(9)in t))&&(n[s]=e[s]);return n};function Sl(e,t,n){const{props:s,children:r,component:i}=e,{props:o,children:l,patchFlag:c}=t,d=i.emitsOptions;if(t.dirs||t.transition)return!0;if(n&&c>=0){if(c&1024)return!0;if(c&16)return s?Vs(s,o,d):!!o;if(c&8){const a=t.dynamicProps;for(let h=0;h<a.length;h++){const g=a[h];if(o[g]!==s[g]&&!On(d,g))return!0}}}else return(r||l)&&(!l||!l.$stable)?!0:s===o?!1:s?o?Vs(s,o,d):!0:!!o;return!1}function Vs(e,t,n){const s=Object.keys(t);if(s.length!==Object.keys(e).length)return!0;for(let r=0;r<s.length;r++){const i=s[r];if(t[i]!==e[i]&&!On(n,i))return!0}return!1}function El({vnode:e,parent:t},n){for(;t;){const s=t.subTree;if(s.suspense&&s.suspense.activeBranch===e&&(s.el=e.el),s===e)(e=t.vnode).el=n,t=t.parent;else break}}const Ei=e=>e.__isSuspense;function wl(e,t){t&&t.pendingBranch?j(e)?t.effects.push(...e):t.effects.push(e):No(e)}const Be=Symbol.for("v-fgt"),Mn=Symbol.for("v-txt"),it=Symbol.for("v-cmt"),ln=Symbol.for("v-stc"),Vt=[];let ve=null;function Rl(e=!1){Vt.push(ve=e?null:[])}function Cl(){Vt.pop(),ve=Vt[Vt.length-1]||null}let qt=1;function Ks(e,t=!1){qt+=e,e<0&&ve&&t&&(ve.hasOnce=!0)}function wi(e){return e.dynamicChildren=qt>0?ve||vt:null,Cl(),qt>0&&ve&&ve.push(e),e}function yf(e,t,n,s,r,i){return wi(Ci(e,t,n,s,r,i,!0))}function Pl(e,t,n,s,r){return wi(pe(e,t,n,s,r,!0))}function _n(e){return e?e.__v_isVNode===!0:!1}function It(e,t){return e.type===t.type&&e.key===t.key}const Ri=({key:e})=>e??null,cn=({ref:e,ref_key:t,ref_for:n})=>(typeof e=="number"&&(e=""+e),e!=null?te(e)||le(e)||L(e)?{i:ye,r:e,k:t,f:!!n}:e:null);function Ci(e,t=null,n=null,s=0,r=null,i=e===Be?0:1,o=!1,l=!1){const c={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&Ri(t),ref:t&&cn(t),scopeId:ti,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:i,patchFlag:s,dynamicProps:r,dynamicChildren:null,appContext:null,ctx:ye};return l?(Es(c,n),i&128&&e.normalize(c)):n&&(c.shapeFlag|=te(n)?8:16),qt>0&&!o&&ve&&(c.patchFlag>0||i&6)&&c.patchFlag!==32&&ve.push(c),c}const pe=Al;function Al(e,t=null,n=null,s=0,r=null,i=!1){if((!e||e===Qo)&&(e=it),_n(e)){const l=Ct(e,t,!0);return n&&Es(l,n),qt>0&&!i&&ve&&(l.shapeFlag&6?ve[ve.indexOf(e)]=l:ve.push(l)),l.patchFlag=-2,l}if(Ll(e)&&(e=e.__vccOpts),t){t=Tl(t);let{class:l,style:c}=t;l&&!te(l)&&(t.class=us(l)),Z(c)&&(_s(c)&&!j(c)&&(c=ce({},c)),t.style=fs(c))}const o=te(e)?1:Ei(e)?128:jo(e)?64:Z(e)?4:L(e)?2:0;return Ci(e,t,n,s,r,o,i,!0)}function Tl(e){return e?_s(e)||hi(e)?ce({},e):e:null}function Ct(e,t,n=!1,s=!1){const{props:r,ref:i,patchFlag:o,children:l,transition:c}=e,d=t?Ml(r||{},t):r,a={__v_isVNode:!0,__v_skip:!0,type:e.type,props:d,key:d&&Ri(d),ref:t&&t.ref?n&&i?j(i)?i.concat(cn(t)):[i,cn(t)]:cn(t):i,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:l,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==Be?o===-1?16:o|16:o,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:c,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&Ct(e.ssContent),ssFallback:e.ssFallback&&Ct(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return c&&s&&bs(a,c.clone(a)),a}function Ol(e=" ",t=0){return pe(Mn,null,e,t)}function vf(e,t){const n=pe(ln,null,e);return n.staticCount=t,n}function bf(e="",t=!1){return t?(Rl(),Pl(it,null,e)):pe(it,null,e)}function Ne(e){return e==null||typeof e=="boolean"?pe(it):j(e)?pe(Be,null,e.slice()):_n(e)?et(e):pe(Mn,null,String(e))}function et(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:Ct(e)}function Es(e,t){let n=0;const{shapeFlag:s}=e;if(t==null)t=null;else if(j(t))n=16;else if(typeof t=="object")if(s&65){const r=t.default;r&&(r._c&&(r._d=!1),Es(e,r()),r._c&&(r._d=!0));return}else{n=32;const r=t._;!r&&!hi(t)?t._ctx=ye:r===3&&ye&&(ye.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else L(t)?(t={default:t,_ctx:ye},n=32):(t=String(t),s&64?(n=16,t=[Ol(t)]):n=8);e.children=t,e.shapeFlag|=n}function Ml(...e){const t={};for(let n=0;n<e.length;n++){const s=e[n];for(const r in s)if(r==="class")t.class!==s.class&&(t.class=us([t.class,s.class]));else if(r==="style")t.style=fs([t.style,s.style]);else if(xn(r)){const i=t[r],o=s[r];o&&i!==o&&!(j(i)&&i.includes(o))&&(t[r]=i?[].concat(i,o):o)}else r!==""&&(t[r]=s[r])}return t}function Ie(e,t,n,s=null){je(e,t,7,[n,s])}const Il=fi();let Fl=0;function Nl(e,t,n){const s=e.type,r=(t?t.appContext:e.appContext)||Il,i={uid:Fl++,vnode:e,type:s,parent:t,appContext:r,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new Or(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(r.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:pi(s,r),emitsOptions:Si(s,r),emit:null,emitted:null,propsDefaults:Q,inheritAttrs:s.inheritAttrs,ctx:Q,data:Q,props:Q,attrs:Q,slots:Q,refs:Q,setupState:Q,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return i.ctx={_:i},i.root=t?t.root:i,i.emit=vl.bind(null,i),e.ce&&e.ce(i),i}let oe=null;const xf=()=>oe||ye;let yn,es;{const e=wn(),t=(n,s)=>{let r;return(r=e[n])||(r=e[n]=[]),r.push(s),i=>{r.length>1?r.forEach(o=>o(i)):r[0](i)}};yn=t("__VUE_INSTANCE_SETTERS__",n=>oe=n),es=t("__VUE_SSR_SETTERS__",n=>Gt=n)}const Xt=e=>{const t=oe;return yn(e),e.scope.on(),()=>{e.scope.off(),yn(t)}},Bs=()=>{oe&&oe.scope.off(),yn(null)};function Pi(e){return e.vnode.shapeFlag&4}let Gt=!1;function Dl(e,t=!1,n=!1){t&&es(t);const{props:s,children:r}=e.vnode,i=Pi(e);il(e,s,i,t),fl(e,r,n||t);const o=i?Hl(e,t):void 0;return t&&es(!1),o}function Hl(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,Yo);const{setup:s}=n;if(s){qe();const r=e.setupContext=s.length>1?$l(e):null,i=Xt(e),o=Yt(s,e,0,[e.props,r]),l=Sr(o);if(Ge(),i(),(l||e.sp)&&!$t(e)&&si(e),l){if(o.then(Bs,Bs),t)return o.then(c=>{Us(e,c)}).catch(c=>{An(c,e,0)});e.asyncDep=o}else Us(e,o)}else Ai(e)}function Us(e,t,n){L(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:Z(t)&&(e.setupState=Qr(t)),Ai(e)}function Ai(e,t,n){const s=e.type;e.render||(e.render=s.render||De);{const r=Xt(e);qe();try{Xo(e)}finally{Ge(),r()}}}const jl={get(e,t){return ie(e,"get",""),e[t]}};function $l(e){const t=n=>{e.exposed=n||{}};return{attrs:new Proxy(e.attrs,jl),slots:e.slots,emit:e.emit,expose:t}}function In(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(Qr(Gr(e.exposed)),{get(t,n){if(n in t)return t[n];if(n in Lt)return Lt[n](e)},has(t,n){return n in t||n in Lt}})):e.proxy}function Ll(e){return L(e)&&"__vccOpts"in e}const Se=(e,t)=>To(e,t,Gt);function Ti(e,t,n){const s=arguments.length;return s===2?Z(t)&&!j(t)?_n(t)?pe(e,null,[t]):pe(e,t):pe(e,null,t):(s>3?n=Array.prototype.slice.call(arguments,2):s===3&&_n(n)&&(n=[n]),pe(e,t,n))}const Vl="3.5.16";/**
* @vue/runtime-dom v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let ts;const ks=typeof window<"u"&&window.trustedTypes;if(ks)try{ts=ks.createPolicy("vue",{createHTML:e=>e})}catch{}const Oi=ts?e=>ts.createHTML(e):e=>e,Kl="http://www.w3.org/2000/svg",Bl="http://www.w3.org/1998/Math/MathML",Ke=typeof document<"u"?document:null,Ws=Ke&&Ke.createElement("template"),Ul={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,s)=>{const r=t==="svg"?Ke.createElementNS(Kl,e):t==="mathml"?Ke.createElementNS(Bl,e):n?Ke.createElement(e,{is:n}):Ke.createElement(e);return e==="select"&&s&&s.multiple!=null&&r.setAttribute("multiple",s.multiple),r},createText:e=>Ke.createTextNode(e),createComment:e=>Ke.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>Ke.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,s,r,i){const o=n?n.previousSibling:t.lastChild;if(r&&(r===i||r.nextSibling))for(;t.insertBefore(r.cloneNode(!0),n),!(r===i||!(r=r.nextSibling)););else{Ws.innerHTML=Oi(s==="svg"?`<svg>${e}</svg>`:s==="mathml"?`<math>${e}</math>`:e);const l=Ws.content;if(s==="svg"||s==="mathml"){const c=l.firstChild;for(;c.firstChild;)l.appendChild(c.firstChild);l.removeChild(c)}t.insertBefore(l,n)}return[o?o.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},kl=Symbol("_vtc");function Wl(e,t,n){const s=e[kl];s&&(t=(t?[t,...s]:[...s]).join(" ")),t==null?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}const qs=Symbol("_vod"),ql=Symbol("_vsh"),Gl=Symbol(""),zl=/(^|;)\s*display\s*:/;function Jl(e,t,n){const s=e.style,r=te(n);let i=!1;if(n&&!r){if(t)if(te(t))for(const o of t.split(";")){const l=o.slice(0,o.indexOf(":")).trim();n[l]==null&&fn(s,l,"")}else for(const o in t)n[o]==null&&fn(s,o,"");for(const o in n)o==="display"&&(i=!0),fn(s,o,n[o])}else if(r){if(t!==n){const o=s[Gl];o&&(n+=";"+o),s.cssText=n,i=zl.test(n)}}else t&&e.removeAttribute("style");qs in e&&(e[qs]=i?s.display:"",e[ql]&&(s.display="none"))}const Gs=/\s*!important$/;function fn(e,t,n){if(j(n))n.forEach(s=>fn(e,t,s));else if(n==null&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const s=Ql(e,t);Gs.test(n)?e.setProperty(ht(s),n.replace(Gs,""),"important"):e[s]=n}}const zs=["Webkit","Moz","ms"],Kn={};function Ql(e,t){const n=Kn[t];if(n)return n;let s=st(t);if(s!=="filter"&&s in e)return Kn[t]=s;s=Rr(s);for(let r=0;r<zs.length;r++){const i=zs[r]+s;if(i in e)return Kn[t]=i}return t}const Js="http://www.w3.org/1999/xlink";function Qs(e,t,n,s,r,i=eo(t)){s&&t.startsWith("xlink:")?n==null?e.removeAttributeNS(Js,t.slice(6,t.length)):e.setAttributeNS(Js,t,n):n==null||i&&!Pr(n)?e.removeAttribute(t):e.setAttribute(t,i?"":He(n)?String(n):n)}function Ys(e,t,n,s,r){if(t==="innerHTML"||t==="textContent"){n!=null&&(e[t]=t==="innerHTML"?Oi(n):n);return}const i=e.tagName;if(t==="value"&&i!=="PROGRESS"&&!i.includes("-")){const l=i==="OPTION"?e.getAttribute("value")||"":e.value,c=n==null?e.type==="checkbox"?"on":"":String(n);(l!==c||!("_value"in e))&&(e.value=c),n==null&&e.removeAttribute(t),e._value=n;return}let o=!1;if(n===""||n==null){const l=typeof e[t];l==="boolean"?n=Pr(n):n==null&&l==="string"?(n="",o=!0):l==="number"&&(n=0,o=!0)}try{e[t]=n}catch{}o&&e.removeAttribute(r||t)}function ut(e,t,n,s){e.addEventListener(t,n,s)}function Yl(e,t,n,s){e.removeEventListener(t,n,s)}const Xs=Symbol("_vei");function Xl(e,t,n,s,r=null){const i=e[Xs]||(e[Xs]={}),o=i[t];if(s&&o)o.value=s;else{const[l,c]=Zl(t);if(s){const d=i[t]=nc(s,r);ut(e,l,d,c)}else o&&(Yl(e,l,o,c),i[t]=void 0)}}const Zs=/(?:Once|Passive|Capture)$/;function Zl(e){let t;if(Zs.test(e)){t={};let s;for(;s=e.match(Zs);)e=e.slice(0,e.length-s[0].length),t[s[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):ht(e.slice(2)),t]}let Bn=0;const ec=Promise.resolve(),tc=()=>Bn||(ec.then(()=>Bn=0),Bn=Date.now());function nc(e,t){const n=s=>{if(!s._vts)s._vts=Date.now();else if(s._vts<=n.attached)return;je(sc(s,n.value),t,5,[s])};return n.value=e,n.attached=tc(),n}function sc(e,t){if(j(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(s=>r=>!r._stopped&&s&&s(r))}else return t}const er=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,rc=(e,t,n,s,r,i)=>{const o=r==="svg";t==="class"?Wl(e,s,o):t==="style"?Jl(e,n,s):xn(t)?os(t)||Xl(e,t,n,s,i):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):ic(e,t,s,o))?(Ys(e,t,s),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&Qs(e,t,s,o,i,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!te(s))?Ys(e,st(t),s,i,t):(t==="true-value"?e._trueValue=s:t==="false-value"&&(e._falseValue=s),Qs(e,t,s,o))};function ic(e,t,n,s){if(s)return!!(t==="innerHTML"||t==="textContent"||t in e&&er(t)&&L(n));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="autocorrect"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const r=e.tagName;if(r==="IMG"||r==="VIDEO"||r==="CANVAS"||r==="SOURCE")return!1}return er(t)&&te(n)?!1:t in e}const vn=e=>{const t=e.props["onUpdate:modelValue"]||!1;return j(t)?n=>sn(t,n):t};function oc(e){e.target.composing=!0}function tr(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const Rt=Symbol("_assign"),Sf={created(e,{modifiers:{lazy:t,trim:n,number:s}},r){e[Rt]=vn(r);const i=s||r.props&&r.props.type==="number";ut(e,t?"change":"input",o=>{if(o.target.composing)return;let l=e.value;n&&(l=l.trim()),i&&(l=un(l)),e[Rt](l)}),n&&ut(e,"change",()=>{e.value=e.value.trim()}),t||(ut(e,"compositionstart",oc),ut(e,"compositionend",tr),ut(e,"change",tr))},mounted(e,{value:t}){e.value=t??""},beforeUpdate(e,{value:t,oldValue:n,modifiers:{lazy:s,trim:r,number:i}},o){if(e[Rt]=vn(o),e.composing)return;const l=(i||e.type==="number")&&!/^0\d/.test(e.value)?un(e.value):e.value,c=t??"";l!==c&&(document.activeElement===e&&e.type!=="range"&&(s&&t===n||r&&e.value.trim()===c)||(e.value=c))}},Ef={deep:!0,created(e,{value:t,modifiers:{number:n}},s){const r=Sn(t);ut(e,"change",()=>{const i=Array.prototype.filter.call(e.options,o=>o.selected).map(o=>n?un(bn(o)):bn(o));e[Rt](e.multiple?r?new Set(i):i:i[0]),e._assigning=!0,ys(()=>{e._assigning=!1})}),e[Rt]=vn(s)},mounted(e,{value:t}){nr(e,t)},beforeUpdate(e,t,n){e[Rt]=vn(n)},updated(e,{value:t}){e._assigning||nr(e,t)}};function nr(e,t){const n=e.multiple,s=j(t);if(!(n&&!s&&!Sn(t))){for(let r=0,i=e.options.length;r<i;r++){const o=e.options[r],l=bn(o);if(n)if(s){const c=typeof l;c==="string"||c==="number"?o.selected=t.some(d=>String(d)===String(l)):o.selected=no(t,l)>-1}else o.selected=t.has(l);else if(Rn(bn(o),t)){e.selectedIndex!==r&&(e.selectedIndex=r);return}}!n&&e.selectedIndex!==-1&&(e.selectedIndex=-1)}}function bn(e){return"_value"in e?e._value:e.value}const lc=["ctrl","shift","alt","meta"],cc={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&e.button!==0,middle:e=>"button"in e&&e.button!==1,right:e=>"button"in e&&e.button!==2,exact:(e,t)=>lc.some(n=>e[`${n}Key`]&&!t.includes(n))},wf=(e,t)=>{const n=e._withMods||(e._withMods={}),s=t.join(".");return n[s]||(n[s]=(r,...i)=>{for(let o=0;o<t.length;o++){const l=cc[t[o]];if(l&&l(r,t))return}return e(r,...i)})},fc=ce({patchProp:rc},Ul);let sr;function uc(){return sr||(sr=al(fc))}const Rf=(...e)=>{const t=uc().createApp(...e),{mount:n}=t;return t.mount=s=>{const r=hc(s);if(!r)return;const i=t._component;!L(i)&&!i.render&&!i.template&&(i.template=r.innerHTML),r.nodeType===1&&(r.textContent="");const o=n(r,!1,ac(r));return r instanceof Element&&(r.removeAttribute("v-cloak"),r.setAttribute("data-v-app","")),o},t};function ac(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function hc(e){return te(e)?document.querySelector(e):e}/*!
 * pinia v3.0.2
 * (c) 2025 Eduardo San Martin Morote
 * @license MIT
 */const dc=Symbol();var rr;(function(e){e.direct="direct",e.patchObject="patch object",e.patchFunction="patch function"})(rr||(rr={}));function Cf(){const e=ro(!0),t=e.run(()=>zr({}));let n=[],s=[];const r=Gr({install(i){r._a=i,i.provide(dc,r),i.config.globalProperties.$pinia=r,s.forEach(o=>n.push(o)),s=[]},use(i){return this._a?n.push(i):s.push(i),this},_p:n,_a:null,_e:e,_s:new Map,state:t});return r}/*!
  * vue-router v4.5.1
  * (c) 2025 Eduardo San Martin Morote
  * @license MIT
  */const yt=typeof document<"u";function Mi(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function pc(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&Mi(e.default)}const U=Object.assign;function Un(e,t){const n={};for(const s in t){const r=t[s];n[s]=we(r)?r.map(e):e(r)}return n}const Kt=()=>{},we=Array.isArray,Ii=/#/g,gc=/&/g,mc=/\//g,_c=/=/g,yc=/\?/g,Fi=/\+/g,vc=/%5B/g,bc=/%5D/g,Ni=/%5E/g,xc=/%60/g,Di=/%7B/g,Sc=/%7C/g,Hi=/%7D/g,Ec=/%20/g;function ws(e){return encodeURI(""+e).replace(Sc,"|").replace(vc,"[").replace(bc,"]")}function wc(e){return ws(e).replace(Di,"{").replace(Hi,"}").replace(Ni,"^")}function ns(e){return ws(e).replace(Fi,"%2B").replace(Ec,"+").replace(Ii,"%23").replace(gc,"%26").replace(xc,"`").replace(Di,"{").replace(Hi,"}").replace(Ni,"^")}function Rc(e){return ns(e).replace(_c,"%3D")}function Cc(e){return ws(e).replace(Ii,"%23").replace(yc,"%3F")}function Pc(e){return e==null?"":Cc(e).replace(mc,"%2F")}function zt(e){try{return decodeURIComponent(""+e)}catch{}return""+e}const Ac=/\/$/,Tc=e=>e.replace(Ac,"");function kn(e,t,n="/"){let s,r={},i="",o="";const l=t.indexOf("#");let c=t.indexOf("?");return l<c&&l>=0&&(c=-1),c>-1&&(s=t.slice(0,c),i=t.slice(c+1,l>-1?l:t.length),r=e(i)),l>-1&&(s=s||t.slice(0,l),o=t.slice(l,t.length)),s=Fc(s??t,n),{fullPath:s+(i&&"?")+i+o,path:s,query:r,hash:zt(o)}}function Oc(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}function ir(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function Mc(e,t,n){const s=t.matched.length-1,r=n.matched.length-1;return s>-1&&s===r&&Pt(t.matched[s],n.matched[r])&&ji(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}function Pt(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function ji(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!Ic(e[n],t[n]))return!1;return!0}function Ic(e,t){return we(e)?or(e,t):we(t)?or(t,e):e===t}function or(e,t){return we(t)?e.length===t.length&&e.every((n,s)=>n===t[s]):e.length===1&&e[0]===t}function Fc(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),s=e.split("/"),r=s[s.length-1];(r===".."||r===".")&&s.push("");let i=n.length-1,o,l;for(o=0;o<s.length;o++)if(l=s[o],l!==".")if(l==="..")i>1&&i--;else break;return n.slice(0,i).join("/")+"/"+s.slice(o).join("/")}const Xe={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var Jt;(function(e){e.pop="pop",e.push="push"})(Jt||(Jt={}));var Bt;(function(e){e.back="back",e.forward="forward",e.unknown=""})(Bt||(Bt={}));function Nc(e){if(!e)if(yt){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),Tc(e)}const Dc=/^[^#]+#/;function Hc(e,t){return e.replace(Dc,"#")+t}function jc(e,t){const n=document.documentElement.getBoundingClientRect(),s=e.getBoundingClientRect();return{behavior:t.behavior,left:s.left-n.left-(t.left||0),top:s.top-n.top-(t.top||0)}}const Fn=()=>({left:window.scrollX,top:window.scrollY});function $c(e){let t;if("el"in e){const n=e.el,s=typeof n=="string"&&n.startsWith("#"),r=typeof n=="string"?s?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!r)return;t=jc(r,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function lr(e,t){return(history.state?history.state.position-t:-1)+e}const ss=new Map;function Lc(e,t){ss.set(e,t)}function Vc(e){const t=ss.get(e);return ss.delete(e),t}let Kc=()=>location.protocol+"//"+location.host;function $i(e,t){const{pathname:n,search:s,hash:r}=t,i=e.indexOf("#");if(i>-1){let l=r.includes(e.slice(i))?e.slice(i).length:1,c=r.slice(l);return c[0]!=="/"&&(c="/"+c),ir(c,"")}return ir(n,e)+s+r}function Bc(e,t,n,s){let r=[],i=[],o=null;const l=({state:g})=>{const m=$i(e,location),A=n.value,T=t.value;let V=0;if(g){if(n.value=m,t.value=g,o&&o===A){o=null;return}V=T?g.position-T.position:0}else s(m);r.forEach(D=>{D(n.value,A,{delta:V,type:Jt.pop,direction:V?V>0?Bt.forward:Bt.back:Bt.unknown})})};function c(){o=n.value}function d(g){r.push(g);const m=()=>{const A=r.indexOf(g);A>-1&&r.splice(A,1)};return i.push(m),m}function a(){const{history:g}=window;g.state&&g.replaceState(U({},g.state,{scroll:Fn()}),"")}function h(){for(const g of i)g();i=[],window.removeEventListener("popstate",l),window.removeEventListener("beforeunload",a)}return window.addEventListener("popstate",l),window.addEventListener("beforeunload",a,{passive:!0}),{pauseListeners:c,listen:d,destroy:h}}function cr(e,t,n,s=!1,r=!1){return{back:e,current:t,forward:n,replaced:s,position:window.history.length,scroll:r?Fn():null}}function Uc(e){const{history:t,location:n}=window,s={value:$i(e,n)},r={value:t.state};r.value||i(s.value,{back:null,current:s.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function i(c,d,a){const h=e.indexOf("#"),g=h>-1?(n.host&&document.querySelector("base")?e:e.slice(h))+c:Kc()+e+c;try{t[a?"replaceState":"pushState"](d,"",g),r.value=d}catch(m){console.error(m),n[a?"replace":"assign"](g)}}function o(c,d){const a=U({},t.state,cr(r.value.back,c,r.value.forward,!0),d,{position:r.value.position});i(c,a,!0),s.value=c}function l(c,d){const a=U({},r.value,t.state,{forward:c,scroll:Fn()});i(a.current,a,!0);const h=U({},cr(s.value,c,null),{position:a.position+1},d);i(c,h,!1),s.value=c}return{location:s,state:r,push:l,replace:o}}function Pf(e){e=Nc(e);const t=Uc(e),n=Bc(e,t.state,t.location,t.replace);function s(i,o=!0){o||n.pauseListeners(),history.go(i)}const r=U({location:"",base:e,go:s,createHref:Hc.bind(null,e)},t,n);return Object.defineProperty(r,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(r,"state",{enumerable:!0,get:()=>t.state.value}),r}function kc(e){return typeof e=="string"||e&&typeof e=="object"}function Li(e){return typeof e=="string"||typeof e=="symbol"}const Vi=Symbol("");var fr;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(fr||(fr={}));function At(e,t){return U(new Error,{type:e,[Vi]:!0},t)}function Ve(e,t){return e instanceof Error&&Vi in e&&(t==null||!!(e.type&t))}const ur="[^/]+?",Wc={sensitive:!1,strict:!1,start:!0,end:!0},qc=/[.+*?^${}()[\]/\\]/g;function Gc(e,t){const n=U({},Wc,t),s=[];let r=n.start?"^":"";const i=[];for(const d of e){const a=d.length?[]:[90];n.strict&&!d.length&&(r+="/");for(let h=0;h<d.length;h++){const g=d[h];let m=40+(n.sensitive?.25:0);if(g.type===0)h||(r+="/"),r+=g.value.replace(qc,"\\$&"),m+=40;else if(g.type===1){const{value:A,repeatable:T,optional:V,regexp:D}=g;i.push({name:A,repeatable:T,optional:V});const I=D||ur;if(I!==ur){m+=10;try{new RegExp(`(${I})`)}catch(O){throw new Error(`Invalid custom RegExp for param "${A}" (${I}): `+O.message)}}let H=T?`((?:${I})(?:/(?:${I}))*)`:`(${I})`;h||(H=V&&d.length<2?`(?:/${H})`:"/"+H),V&&(H+="?"),r+=H,m+=20,V&&(m+=-8),T&&(m+=-20),I===".*"&&(m+=-50)}a.push(m)}s.push(a)}if(n.strict&&n.end){const d=s.length-1;s[d][s[d].length-1]+=.7000000000000001}n.strict||(r+="/?"),n.end?r+="$":n.strict&&!r.endsWith("/")&&(r+="(?:/|$)");const o=new RegExp(r,n.sensitive?"":"i");function l(d){const a=d.match(o),h={};if(!a)return null;for(let g=1;g<a.length;g++){const m=a[g]||"",A=i[g-1];h[A.name]=m&&A.repeatable?m.split("/"):m}return h}function c(d){let a="",h=!1;for(const g of e){(!h||!a.endsWith("/"))&&(a+="/"),h=!1;for(const m of g)if(m.type===0)a+=m.value;else if(m.type===1){const{value:A,repeatable:T,optional:V}=m,D=A in d?d[A]:"";if(we(D)&&!T)throw new Error(`Provided param "${A}" is an array but it is not repeatable (* or + modifiers)`);const I=we(D)?D.join("/"):D;if(!I)if(V)g.length<2&&(a.endsWith("/")?a=a.slice(0,-1):h=!0);else throw new Error(`Missing required param "${A}"`);a+=I}}return a||"/"}return{re:o,score:s,keys:i,parse:l,stringify:c}}function zc(e,t){let n=0;for(;n<e.length&&n<t.length;){const s=t[n]-e[n];if(s)return s;n++}return e.length<t.length?e.length===1&&e[0]===80?-1:1:e.length>t.length?t.length===1&&t[0]===80?1:-1:0}function Ki(e,t){let n=0;const s=e.score,r=t.score;for(;n<s.length&&n<r.length;){const i=zc(s[n],r[n]);if(i)return i;n++}if(Math.abs(r.length-s.length)===1){if(ar(s))return 1;if(ar(r))return-1}return r.length-s.length}function ar(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const Jc={type:0,value:""},Qc=/[a-zA-Z0-9_]/;function Yc(e){if(!e)return[[]];if(e==="/")return[[Jc]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(m){throw new Error(`ERR (${n})/"${d}": ${m}`)}let n=0,s=n;const r=[];let i;function o(){i&&r.push(i),i=[]}let l=0,c,d="",a="";function h(){d&&(n===0?i.push({type:0,value:d}):n===1||n===2||n===3?(i.length>1&&(c==="*"||c==="+")&&t(`A repeatable param (${d}) must be alone in its segment. eg: '/:ids+.`),i.push({type:1,value:d,regexp:a,repeatable:c==="*"||c==="+",optional:c==="*"||c==="?"})):t("Invalid state to consume buffer"),d="")}function g(){d+=c}for(;l<e.length;){if(c=e[l++],c==="\\"&&n!==2){s=n,n=4;continue}switch(n){case 0:c==="/"?(d&&h(),o()):c===":"?(h(),n=1):g();break;case 4:g(),n=s;break;case 1:c==="("?n=2:Qc.test(c)?g():(h(),n=0,c!=="*"&&c!=="?"&&c!=="+"&&l--);break;case 2:c===")"?a[a.length-1]=="\\"?a=a.slice(0,-1)+c:n=3:a+=c;break;case 3:h(),n=0,c!=="*"&&c!=="?"&&c!=="+"&&l--,a="";break;default:t("Unknown state");break}}return n===2&&t(`Unfinished custom RegExp for param "${d}"`),h(),o(),r}function Xc(e,t,n){const s=Gc(Yc(e.path),n),r=U(s,{record:e,parent:t,children:[],alias:[]});return t&&!r.record.aliasOf==!t.record.aliasOf&&t.children.push(r),r}function Zc(e,t){const n=[],s=new Map;t=gr({strict:!1,end:!0,sensitive:!1},t);function r(h){return s.get(h)}function i(h,g,m){const A=!m,T=dr(h);T.aliasOf=m&&m.record;const V=gr(t,h),D=[T];if("alias"in h){const O=typeof h.alias=="string"?[h.alias]:h.alias;for(const z of O)D.push(dr(U({},T,{components:m?m.record.components:T.components,path:z,aliasOf:m?m.record:T})))}let I,H;for(const O of D){const{path:z}=O;if(g&&z[0]!=="/"){const se=g.record.path,ee=se[se.length-1]==="/"?"":"/";O.path=g.record.path+(z&&ee+z)}if(I=Xc(O,g,V),m?m.alias.push(I):(H=H||I,H!==I&&H.alias.push(I),A&&h.name&&!pr(I)&&o(h.name)),Bi(I)&&c(I),T.children){const se=T.children;for(let ee=0;ee<se.length;ee++)i(se[ee],I,m&&m.children[ee])}m=m||I}return H?()=>{o(H)}:Kt}function o(h){if(Li(h)){const g=s.get(h);g&&(s.delete(h),n.splice(n.indexOf(g),1),g.children.forEach(o),g.alias.forEach(o))}else{const g=n.indexOf(h);g>-1&&(n.splice(g,1),h.record.name&&s.delete(h.record.name),h.children.forEach(o),h.alias.forEach(o))}}function l(){return n}function c(h){const g=nf(h,n);n.splice(g,0,h),h.record.name&&!pr(h)&&s.set(h.record.name,h)}function d(h,g){let m,A={},T,V;if("name"in h&&h.name){if(m=s.get(h.name),!m)throw At(1,{location:h});V=m.record.name,A=U(hr(g.params,m.keys.filter(H=>!H.optional).concat(m.parent?m.parent.keys.filter(H=>H.optional):[]).map(H=>H.name)),h.params&&hr(h.params,m.keys.map(H=>H.name))),T=m.stringify(A)}else if(h.path!=null)T=h.path,m=n.find(H=>H.re.test(T)),m&&(A=m.parse(T),V=m.record.name);else{if(m=g.name?s.get(g.name):n.find(H=>H.re.test(g.path)),!m)throw At(1,{location:h,currentLocation:g});V=m.record.name,A=U({},g.params,h.params),T=m.stringify(A)}const D=[];let I=m;for(;I;)D.unshift(I.record),I=I.parent;return{name:V,path:T,params:A,matched:D,meta:tf(D)}}e.forEach(h=>i(h));function a(){n.length=0,s.clear()}return{addRoute:i,resolve:d,removeRoute:o,clearRoutes:a,getRoutes:l,getRecordMatcher:r}}function hr(e,t){const n={};for(const s of t)s in e&&(n[s]=e[s]);return n}function dr(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:ef(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function ef(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const s in e.components)t[s]=typeof n=="object"?n[s]:n;return t}function pr(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function tf(e){return e.reduce((t,n)=>U(t,n.meta),{})}function gr(e,t){const n={};for(const s in e)n[s]=s in t?t[s]:e[s];return n}function nf(e,t){let n=0,s=t.length;for(;n!==s;){const i=n+s>>1;Ki(e,t[i])<0?s=i:n=i+1}const r=sf(e);return r&&(s=t.lastIndexOf(r,s-1)),s}function sf(e){let t=e;for(;t=t.parent;)if(Bi(t)&&Ki(e,t)===0)return t}function Bi({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function rf(e){const t={};if(e===""||e==="?")return t;const s=(e[0]==="?"?e.slice(1):e).split("&");for(let r=0;r<s.length;++r){const i=s[r].replace(Fi," "),o=i.indexOf("="),l=zt(o<0?i:i.slice(0,o)),c=o<0?null:zt(i.slice(o+1));if(l in t){let d=t[l];we(d)||(d=t[l]=[d]),d.push(c)}else t[l]=c}return t}function mr(e){let t="";for(let n in e){const s=e[n];if(n=Rc(n),s==null){s!==void 0&&(t+=(t.length?"&":"")+n);continue}(we(s)?s.map(i=>i&&ns(i)):[s&&ns(s)]).forEach(i=>{i!==void 0&&(t+=(t.length?"&":"")+n,i!=null&&(t+="="+i))})}return t}function of(e){const t={};for(const n in e){const s=e[n];s!==void 0&&(t[n]=we(s)?s.map(r=>r==null?null:""+r):s==null?s:""+s)}return t}const lf=Symbol(""),_r=Symbol(""),Rs=Symbol(""),Ui=Symbol(""),rs=Symbol("");function Ft(){let e=[];function t(s){return e.push(s),()=>{const r=e.indexOf(s);r>-1&&e.splice(r,1)}}function n(){e=[]}return{add:t,list:()=>e.slice(),reset:n}}function tt(e,t,n,s,r,i=o=>o()){const o=s&&(s.enterCallbacks[r]=s.enterCallbacks[r]||[]);return()=>new Promise((l,c)=>{const d=g=>{g===!1?c(At(4,{from:n,to:t})):g instanceof Error?c(g):kc(g)?c(At(2,{from:t,to:g})):(o&&s.enterCallbacks[r]===o&&typeof g=="function"&&o.push(g),l())},a=i(()=>e.call(s&&s.instances[r],t,n,d));let h=Promise.resolve(a);e.length<3&&(h=h.then(d)),h.catch(g=>c(g))})}function Wn(e,t,n,s,r=i=>i()){const i=[];for(const o of e)for(const l in o.components){let c=o.components[l];if(!(t!=="beforeRouteEnter"&&!o.instances[l]))if(Mi(c)){const a=(c.__vccOpts||c)[t];a&&i.push(tt(a,n,s,o,l,r))}else{let d=c();i.push(()=>d.then(a=>{if(!a)throw new Error(`Couldn't resolve component "${l}" at "${o.path}"`);const h=pc(a)?a.default:a;o.mods[l]=a,o.components[l]=h;const m=(h.__vccOpts||h)[t];return m&&tt(m,n,s,o,l,r)()}))}}return i}function yr(e){const t=We(Rs),n=We(Ui),s=Se(()=>{const c=St(e.to);return t.resolve(c)}),r=Se(()=>{const{matched:c}=s.value,{length:d}=c,a=c[d-1],h=n.matched;if(!a||!h.length)return-1;const g=h.findIndex(Pt.bind(null,a));if(g>-1)return g;const m=vr(c[d-2]);return d>1&&vr(a)===m&&h[h.length-1].path!==m?h.findIndex(Pt.bind(null,c[d-2])):g}),i=Se(()=>r.value>-1&&hf(n.params,s.value.params)),o=Se(()=>r.value>-1&&r.value===n.matched.length-1&&ji(n.params,s.value.params));function l(c={}){if(af(c)){const d=t[St(e.replace)?"replace":"push"](St(e.to)).catch(Kt);return e.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>d),d}return Promise.resolve()}return{route:s,href:Se(()=>s.value.href),isActive:i,isExactActive:o,navigate:l}}function cf(e){return e.length===1?e[0]:e}const ff=ni({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:yr,setup(e,{slots:t}){const n=Pn(yr(e)),{options:s}=We(Rs),r=Se(()=>({[br(e.activeClass,s.linkActiveClass,"router-link-active")]:n.isActive,[br(e.exactActiveClass,s.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive}));return()=>{const i=t.default&&cf(t.default(n));return e.custom?i:Ti("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:r.value},i)}}}),uf=ff;function af(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function hf(e,t){for(const n in t){const s=t[n],r=e[n];if(typeof s=="string"){if(s!==r)return!1}else if(!we(r)||r.length!==s.length||s.some((i,o)=>i!==r[o]))return!1}return!0}function vr(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const br=(e,t,n)=>e??t??n,df=ni({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const s=We(rs),r=Se(()=>e.route||s.value),i=We(_r,0),o=Se(()=>{let d=St(i);const{matched:a}=r.value;let h;for(;(h=a[d])&&!h.components;)d++;return d}),l=Se(()=>r.value.matched[o.value]);rn(_r,Se(()=>o.value+1)),rn(lf,l),rn(rs,r);const c=zr();return on(()=>[c.value,l.value,e.name],([d,a,h],[g,m,A])=>{a&&(a.instances[h]=d,m&&m!==a&&d&&d===g&&(a.leaveGuards.size||(a.leaveGuards=m.leaveGuards),a.updateGuards.size||(a.updateGuards=m.updateGuards))),d&&a&&(!m||!Pt(a,m)||!g)&&(a.enterCallbacks[h]||[]).forEach(T=>T(d))},{flush:"post"}),()=>{const d=r.value,a=e.name,h=l.value,g=h&&h.components[a];if(!g)return xr(n.default,{Component:g,route:d});const m=h.props[a],A=m?m===!0?d.params:typeof m=="function"?m(d):m:null,V=Ti(g,U({},A,t,{onVnodeUnmounted:D=>{D.component.isUnmounted&&(h.instances[a]=null)},ref:c}));return xr(n.default,{Component:V,route:d})||V}}});function xr(e,t){if(!e)return null;const n=e(t);return n.length===1?n[0]:n}const pf=df;function Af(e){const t=Zc(e.routes,e),n=e.parseQuery||rf,s=e.stringifyQuery||mr,r=e.history,i=Ft(),o=Ft(),l=Ft(),c=Ro(Xe);let d=Xe;yt&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const a=Un.bind(null,y=>""+y),h=Un.bind(null,Pc),g=Un.bind(null,zt);function m(y,P){let R,M;return Li(y)?(R=t.getRecordMatcher(y),M=P):M=y,t.addRoute(M,R)}function A(y){const P=t.getRecordMatcher(y);P&&t.removeRoute(P)}function T(){return t.getRoutes().map(y=>y.record)}function V(y){return!!t.getRecordMatcher(y)}function D(y,P){if(P=U({},P||c.value),typeof y=="string"){const p=kn(n,y,P.path),_=t.resolve({path:p.path},P),b=r.createHref(p.fullPath);return U(p,_,{params:g(_.params),hash:zt(p.hash),redirectedFrom:void 0,href:b})}let R;if(y.path!=null)R=U({},y,{path:kn(n,y.path,P.path).path});else{const p=U({},y.params);for(const _ in p)p[_]==null&&delete p[_];R=U({},y,{params:h(p)}),P.params=h(P.params)}const M=t.resolve(R,P),J=y.hash||"";M.params=a(g(M.params));const f=Oc(s,U({},y,{hash:wc(J),path:M.path})),u=r.createHref(f);return U({fullPath:f,hash:J,query:s===mr?of(y.query):y.query||{}},M,{redirectedFrom:void 0,href:u})}function I(y){return typeof y=="string"?kn(n,y,c.value.path):U({},y)}function H(y,P){if(d!==y)return At(8,{from:P,to:y})}function O(y){return ee(y)}function z(y){return O(U(I(y),{replace:!0}))}function se(y){const P=y.matched[y.matched.length-1];if(P&&P.redirect){const{redirect:R}=P;let M=typeof R=="function"?R(y):R;return typeof M=="string"&&(M=M.includes("?")||M.includes("#")?M=I(M):{path:M},M.params={}),U({query:y.query,hash:y.hash,params:M.path!=null?{}:y.params},M)}}function ee(y,P){const R=d=D(y),M=c.value,J=y.state,f=y.force,u=y.replace===!0,p=se(R);if(p)return ee(U(I(p),{state:typeof p=="object"?U({},J,p.state):J,force:f,replace:u}),P||R);const _=R;_.redirectedFrom=P;let b;return!f&&Mc(s,M,R)&&(b=At(16,{to:_,from:M}),Ae(M,M,!0,!1)),(b?Promise.resolve(b):Ce(_,M)).catch(v=>Ve(v)?Ve(v,2)?v:Ye(v):B(v,_,M)).then(v=>{if(v){if(Ve(v,2))return ee(U({replace:u},I(v.to),{state:typeof v.to=="object"?U({},J,v.to.state):J,force:f}),P||_)}else v=ot(_,M,!0,u,J);return Qe(_,M,v),v})}function Re(y,P){const R=H(y,P);return R?Promise.reject(R):Promise.resolve()}function Je(y){const P=gt.values().next().value;return P&&typeof P.runWithContext=="function"?P.runWithContext(y):y()}function Ce(y,P){let R;const[M,J,f]=gf(y,P);R=Wn(M.reverse(),"beforeRouteLeave",y,P);for(const p of M)p.leaveGuards.forEach(_=>{R.push(tt(_,y,P))});const u=Re.bind(null,y,P);return R.push(u),be(R).then(()=>{R=[];for(const p of i.list())R.push(tt(p,y,P));return R.push(u),be(R)}).then(()=>{R=Wn(J,"beforeRouteUpdate",y,P);for(const p of J)p.updateGuards.forEach(_=>{R.push(tt(_,y,P))});return R.push(u),be(R)}).then(()=>{R=[];for(const p of f)if(p.beforeEnter)if(we(p.beforeEnter))for(const _ of p.beforeEnter)R.push(tt(_,y,P));else R.push(tt(p.beforeEnter,y,P));return R.push(u),be(R)}).then(()=>(y.matched.forEach(p=>p.enterCallbacks={}),R=Wn(f,"beforeRouteEnter",y,P,Je),R.push(u),be(R))).then(()=>{R=[];for(const p of o.list())R.push(tt(p,y,P));return R.push(u),be(R)}).catch(p=>Ve(p,8)?p:Promise.reject(p))}function Qe(y,P,R){l.list().forEach(M=>Je(()=>M(y,P,R)))}function ot(y,P,R,M,J){const f=H(y,P);if(f)return f;const u=P===Xe,p=yt?history.state:{};R&&(M||u?r.replace(y.fullPath,U({scroll:u&&p&&p.scroll},J)):r.push(y.fullPath,J)),c.value=y,Ae(y,P,R,u),Ye()}let Pe;function Tt(){Pe||(Pe=r.listen((y,P,R)=>{if(!Zt.listening)return;const M=D(y),J=se(M);if(J){ee(U(J,{replace:!0,force:!0}),M).catch(Kt);return}d=M;const f=c.value;yt&&Lc(lr(f.fullPath,R.delta),Fn()),Ce(M,f).catch(u=>Ve(u,12)?u:Ve(u,2)?(ee(U(I(u.to),{force:!0}),M).then(p=>{Ve(p,20)&&!R.delta&&R.type===Jt.pop&&r.go(-1,!1)}).catch(Kt),Promise.reject()):(R.delta&&r.go(-R.delta,!1),B(u,M,f))).then(u=>{u=u||ot(M,f,!1),u&&(R.delta&&!Ve(u,8)?r.go(-R.delta,!1):R.type===Jt.pop&&Ve(u,20)&&r.go(-1,!1)),Qe(M,f,u)}).catch(Kt)}))}let dt=Ft(),ne=Ft(),G;function B(y,P,R){Ye(y);const M=ne.list();return M.length?M.forEach(J=>J(y,P,R)):console.error(y),Promise.reject(y)}function $e(){return G&&c.value!==Xe?Promise.resolve():new Promise((y,P)=>{dt.add([y,P])})}function Ye(y){return G||(G=!y,Tt(),dt.list().forEach(([P,R])=>y?R(y):P()),dt.reset()),y}function Ae(y,P,R,M){const{scrollBehavior:J}=e;if(!yt||!J)return Promise.resolve();const f=!R&&Vc(lr(y.fullPath,0))||(M||!R)&&history.state&&history.state.scroll||null;return ys().then(()=>J(y,P,f)).then(u=>u&&$c(u)).catch(u=>B(u,y,P))}const ae=y=>r.go(y);let pt;const gt=new Set,Zt={currentRoute:c,listening:!0,addRoute:m,removeRoute:A,clearRoutes:t.clearRoutes,hasRoute:V,getRoutes:T,resolve:D,options:e,push:O,replace:z,go:ae,back:()=>ae(-1),forward:()=>ae(1),beforeEach:i.add,beforeResolve:o.add,afterEach:l.add,onError:ne.add,isReady:$e,install(y){const P=this;y.component("RouterLink",uf),y.component("RouterView",pf),y.config.globalProperties.$router=P,Object.defineProperty(y.config.globalProperties,"$route",{enumerable:!0,get:()=>St(c)}),yt&&!pt&&c.value===Xe&&(pt=!0,O(r.location).catch(J=>{}));const R={};for(const J in Xe)Object.defineProperty(R,J,{get:()=>c.value[J],enumerable:!0});y.provide(Rs,P),y.provide(Ui,Wr(R)),y.provide(rs,c);const M=y.unmount;gt.add(y),y.unmount=function(){gt.delete(y),gt.size<1&&(d=Xe,Pe&&Pe(),Pe=null,c.value=Xe,pt=!1,G=!1),M()}}};function be(y){return y.reduce((P,R)=>P.then(()=>Je(R)),Promise.resolve())}return Zt}function gf(e,t){const n=[],s=[],r=[],i=Math.max(t.matched.length,e.matched.length);for(let o=0;o<i;o++){const l=t.matched[o];l&&(e.matched.find(d=>Pt(d,l))?s.push(l):n.push(l));const c=e.matched[o];c&&(t.matched.find(d=>Pt(d,c))||r.push(c))}return[n,s,r]}export{bf as A,Af as B,Pf as C,Rf as D,Cf as E,Be as F,Pn as G,wf as H,mf as I,Sf as J,Ef as K,uf as R,Mn as T,We as a,oi as b,Se as c,ni as d,ro as e,Ko as f,xf as g,Ti as h,le as i,pe as j,yf as k,Ci as l,Do as m,us as n,Bo as o,Rl as p,Ol as q,zr as r,Ro as s,so as t,St as u,vf as v,on as w,pf as x,_f as y,fs as z};
