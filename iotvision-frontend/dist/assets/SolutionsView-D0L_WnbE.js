import{d,k as o,l as t,t as s,F as c,y as r,j as _,m,q as p,u as h,R as v,p as n}from"./vendor-Cao0-QMi.js";import{_ as y}from"./index-nRGPD0fz.js";import"./i18n-laVgjn-G.js";const g={class:"solutions-view"},f={class:"solutions-hero section"},b={class:"container"},K={class:"hero-content"},k={class:"hero-title"},E={class:"hero-subtitle"},S={class:"solutions-grid section"},w={class:"container"},C={class:"solutions-list"},V={class:"solution-icon"},$={class:"solution-title"},I={class:"solution-description"},B={class:"solution-features"},F={class:"solution-stats"},N={class:"stat"},R={class:"stat-value"},T={class:"stat-label"},z=d({__name:"SolutionsView",setup(A){const u=[{id:1,titleKey:"solutions.manufacturing.title",icon:"🏭",descriptionKey:"solutions.manufacturing.description",features:["Predictive maintenance","Quality control automation","Energy optimization","Supply chain visibility"],stat:{value:"40%",label:"Efficiency Increase"}},{id:2,titleKey:"solutions.smartCities.title",icon:"🏙️",descriptionKey:"solutions.smartCities.description",features:["Traffic flow optimization","Smart lighting systems","Waste management","Environmental monitoring"],stat:{value:"30%",label:"Energy Savings"}},{id:3,titleKey:"solutions.healthcare.title",icon:"🏥",descriptionKey:"solutions.healthcare.description",features:["Patient monitoring","Asset tracking","Environmental control","Emergency response"],stat:{value:"50%",label:"Response Time Improvement"}},{id:4,titleKey:"solutions.agriculture.title",icon:"🌾",descriptionKey:"solutions.agriculture.description",features:["Soil monitoring","Automated irrigation","Crop health tracking","Weather integration"],stat:{value:"25%",label:"Yield Increase"}}];return(i,a)=>(n(),o("div",g,[t("section",f,[t("div",b,[t("div",K,[t("h1",k,s(i.$t("solutions.title")),1),t("p",E,s(i.$t("solutions.subtitle")),1)])])]),t("section",S,[t("div",w,[t("div",C,[(n(),o(c,null,r(u,e=>t("div",{key:e.id,class:"solution-card"},[t("div",V,s(e.icon),1),t("h3",$,s(i.$t(e.titleKey)),1),t("p",I,s(i.$t(e.descriptionKey)),1),t("div",B,[a[0]||(a[0]=t("h4",null,"Key Features:",-1)),t("ul",null,[(n(!0),o(c,null,r(e.features,l=>(n(),o("li",{key:l},s(l),1))),128))])]),t("div",F,[t("div",N,[t("span",R,s(e.stat.value),1),t("span",T,s(e.stat.label),1)])]),_(h(v),{to:"/contact",class:"btn btn-outline solution-cta"},{default:m(()=>[p(s(i.$t("common.learnMore")),1)]),_:1})])),64))])])])]))}}),j=y(z,[["__scopeId","data-v-0a197b76"]]);export{j as default};
